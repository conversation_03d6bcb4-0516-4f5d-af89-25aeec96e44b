# 多种报告格式功能实现文档

## 功能概述

Deep Research 项目现已支持多种报告格式生成，用户可以根据不同需求选择合适的报告格式，包括标准综合报告、担保品分析报告和自定义格式报告。

## 核心特性

### 1. 分层Prompt架构
采用三层架构设计，确保灵活性和可扩展性：

- **核心数据层 (Core Data Layer)**: 包含研究过程中收集的所有数据
- **计划参考层 (Plan Reference Layer)**: 可配置的原始研究计划影响权重
- **格式指导层 (Format Instruction Layer)**: 特定报告格式的指导说明

### 2. 支持的报告格式

#### 标准综合报告 (Standard Report)
- **特点**: 基于原始研究计划的详细综合报告
- **适用场景**: 全面了解研究主题
- **Plan权重**: Strong (严格遵循原始计划)
- **图标**: 📄

#### 担保品分析报告 (Collateral Analysis Report)  
- **特点**: 专业的金融担保品分析报告格式
- **适用场景**: 机构研究、金融分析
- **Plan权重**: Weak (参考原计划但优先格式要求)
- **图标**: 📊
- **特殊结构**: 
  - 背景概述 (Background Overview)
  - 因果分析 (Causal Analysis) 
  - 影响评估 (Impact Assessment)
  - 结论建议 (Conclusions & Recommendations)

#### 自定义格式 (Custom Format)
- **特点**: 用户自定义的个性化报告格式
- **适用场景**: 特殊需求、个性化报告
- **Plan权重**: 可配置 (Strong/Weak/None)
- **图标**: ✏️

### 3. 自定义格式功能

#### 预设模板
系统提供两个预设的自定义格式模板：

1. **执行摘要 (Executive Summary)**
   - 执行摘要、关键发现、建议、支撑证据、下一步行动
   - 适合决策者快速阅读

2. **学术论文 (Academic Paper)**
   - 摘要、引言、文献综述、方法论、发现分析、讨论、结论、参考文献
   - 适合学术研究和正式发表

#### 自定义Prompt输入
- 支持用户直接输入自定义格式指导prompt
- 提供prompt编写指南和最佳实践
- 包含prompt验证功能，确保输入质量

#### 格式管理
- 保存和管理用户自定义的格式
- 支持格式的创建、编辑、删除
- 格式历史记录和版本管理

## 技术实现

### 文件结构

```
src/
├── types/report.ts                           # 报告类型定义
├── utils/deep-research/report-formats.ts    # 报告格式配置和prompt构建
├── store/task.ts                            # 状态管理 (已修改)
├── hooks/useDeepResearch.ts                 # 报告生成逻辑 (已修改)
├── components/Research/FinalReport/index.tsx # UI组件 (已修改)
└── locales/                                 # 国际化文件 (已修改)
    ├── zh-CN.json
    └── en-US.json
```

### 核心API

#### buildFinalReportPrompt()
```typescript
function buildFinalReportPrompt(
  reportType: ReportType,
  data: {
    learnings: string;
    sources: any[];
    images: any[];
    plan: string;
    requirement: string;
  },
  customFormat?: CustomReportFormat,
  planWeight?: PlanWeight
): string
```

#### validateCustomPrompt()
```typescript
function validateCustomPrompt(prompt: string): {
  isValid: boolean;
  errors: string[];
}
```

### 状态管理

新增的状态字段：
- `reportType`: 当前选择的报告类型
- `customPrompt`: 用户输入的自定义prompt
- `customFormats`: 保存的自定义格式列表
- `selectedCustomFormatId`: 选中的自定义格式ID

### UI组件

#### 报告类型选择器
- 下拉选择框，显示所有可用的报告格式
- 每个选项包含图标、名称和描述
- 支持实时切换和预览

#### 自定义格式输入
- 当选择自定义格式时显示
- 支持选择预设模板或直接输入
- 提供prompt编写指南
- 实时验证输入内容

## 使用指南

### 基本使用流程

1. **完成研究**: 确保所有搜索任务已完成
2. **选择格式**: 在最终报告区域选择所需的报告格式
3. **配置选项**: 
   - 标准格式：直接生成
   - 担保品分析：直接生成
   - 自定义格式：选择模板或输入自定义prompt
4. **生成报告**: 点击生成按钮开始生成报告

### 自定义Prompt编写指南

#### 最佳实践
1. **明确结构**: 使用清晰的标题和章节
2. **具体要求**: 指定期望的长度、语调和风格
3. **提供上下文**: 说明报告目的和目标受众
4. **使用示例**: 在需要时提供格式示例
5. **避免冲突**: 保持指令的一致性和连贯性

#### 示例模板
```
撰写一份 [类型] 报告，包含以下结构：

## 第一部分：[名称]
- [具体要求]
- [期望内容类型]

## 第二部分：[名称]  
- [具体要求]
- [期望内容类型]

使用 [语调] 语言，面向 [受众]。
目标长度：[长度] 页。
```

## 国际化支持

支持中英文双语：
- 界面文本完全本地化
- 报告格式名称和描述翻译
- Prompt指南和帮助文本翻译

## 向后兼容性

- 保持现有标准报告格式不变
- 默认报告类型为"标准格式"
- 现有用户数据和设置不受影响

## 扩展性设计

### 添加新的报告格式

1. **更新类型定义** (`src/types/report.ts`):
```typescript
export type ReportType = 'standard' | 'collateral-analysis' | 'custom' | 'new-format';
```

2. **添加格式配置** (`src/types/report.ts`):
```typescript
'new-format': {
  type: 'new-format',
  planWeight: 'weak',
  name: 'New Format',
  description: 'Description of new format',
  icon: '🆕'
}
```

3. **定义格式指导** (`src/utils/deep-research/report-formats.ts`):
```typescript
'new-format': `Your format instructions here...`
```

4. **添加翻译文本** (`src/locales/`):
```json
"reportTypes": {
  "new-format": "新格式名称"
},
"reportTypeDescriptions": {
  "new-format": "新格式描述"
}
```

## 测试验证

### 功能测试清单
- [ ] 标准格式报告生成正常
- [ ] 担保品分析格式报告生成正常  
- [ ] 自定义格式（预设模板）生成正常
- [ ] 自定义格式（直接输入）生成正常
- [ ] 格式切换功能正常
- [ ] Prompt验证功能正常
- [ ] 国际化显示正常
- [ ] 状态保存和恢复正常

### 性能测试
- 报告生成时间在可接受范围内
- UI响应流畅，无明显卡顿
- 内存使用合理

## 已知限制

1. 自定义prompt长度限制为5000字符
2. 同时只能选择一种报告格式
3. 格式切换需要重新生成报告

## 未来改进方向

1. 支持报告格式的导入/导出
2. 添加更多预设格式模板
3. 支持格式的在线分享和社区
4. 增加报告格式的预览功能
5. 支持多语言prompt模板
