# Deep Research 自动研究功能实现计划

## 📋 项目概述

在现有Deep Research流程基础上，添加自动研究功能。原流程：用户输入 → 澄清问题 → 用户回复 → 生成plan → 生成SERP查询 → 信息收集 → 用户确认 → 最终报告。

新增流程：生成plan → **[自动研究选项]** → 初始查询 → LLM反思 → LLM评估 → 循环/停止 → 最终报告。

## 🎯 设计原则

1. **最大兼容性**: 复用现有技术栈和数据结构，不影响手动研究流程
2. **流程可见**: 复用现有SearchResult展示，每轮查询、反思、评估都可见
3. **用户控制**: 提供配置选项和停止机制
4. **渐进实现**: 分阶段开发，确保每阶段功能完整

## 🏗️ 技术架构

### 技术栈保持
- **前端**: React + Zustand状态管理
- **后端**: 扩展现有useDeepResearch hook和DeepResearch类  
- **数据存储**: 复用useTaskStore，添加自动研究状态
- **API**: 扩展现有MCP服务器工具

### 核心数据结构
```typescript
// 扩展TaskStore
interface AutoResearchState {
  config: {
    enabled: boolean;
    maxRounds: number;        // 默认3
    initialQueries: number;   // 默认5  
    qualityThreshold: number; // 默认0.7
    maxDuration: number;      // 默认30分钟
  };
  runtime: {
    isRunning: boolean;
    currentRound: number;
    startTime: number;
  };
}

// 扩展SearchTask
interface SearchTask {
  // 现有字段...
  roundNumber?: number;
  taskType?: 'search' | 'reflection' | 'evaluation';
}
```

## 🔄 核心功能模块

### 1. 反思模块Prompt (基于Gemini改写)
```typescript
const autoResearchReflectionPrompt = `You are an expert research assistant analyzing current research findings about "{research_topic}".

Your task is to identify knowledge gaps and determine if additional research is needed.

Instructions:
- Analyze the completeness of current research findings
- Identify specific areas that need deeper exploration  
- Generate targeted follow-up queries if information is insufficient
- Consider technical details, recent developments, and implementation specifics
- Current date: {current_date}

Research Plan Context: {research_plan}
Current Research Findings: {current_findings}

Output Format (JSON only):
{
  "is_sufficient": boolean,
  "knowledge_gap": "specific description of what information is missing",
  "follow_up_queries": ["targeted search query 1", "targeted search query 2"],
  "confidence_score": number // 0-1, confidence in current findings
}`;
```

### 2. 评估模块Prompt
```typescript
const autoResearchEvaluationPrompt = `Evaluate the overall quality and completeness of research findings for "{research_topic}".

Evaluation Criteria:
- Information completeness and coverage
- Source credibility and recency  
- Depth of technical analysis
- Relevance to research objectives

Research Context: {research_plan}
All Research Findings: {all_findings}

Output Format (JSON only):
{
  "overall_score": number, // 0-1
  "completeness_score": number, // 0-1  
  "quality_score": number, // 0-1
  "should_continue": boolean,
  "summary": "brief evaluation summary",
  "recommendations": ["specific improvement suggestions"]
}`;
```

### 3. 自动研究主流程
```typescript
const startAutoResearch = async () => {
  // 第1轮：基于plan生成初始查询（复用现有逻辑）
  const initialQueries = await deepResearch();
  
  let currentRound = 1;
  let shouldContinue = true;
  
  while (shouldContinue && currentRound <= config.maxRounds) {
    // 执行当前轮次的搜索任务
    await runSearchTasks(getCurrentRoundQueries(currentRound));
    
    // 反思分析
    const reflection = await performReflection(currentRound);
    addReflectionTask(reflection, currentRound);
    
    // 质量评估  
    const evaluation = await performEvaluation(currentRound);
    addEvaluationTask(evaluation, currentRound);
    
    // 判断是否继续
    shouldContinue = evaluation.should_continue && 
                    evaluation.overall_score < config.qualityThreshold &&
                    !isTimeExceeded();
    
    if (shouldContinue && reflection.follow_up_queries.length > 0) {
      // 生成下一轮查询并添加到任务列表
      const nextRoundQueries = generateNextRoundQueries(reflection, currentRound + 1);
      taskStore.addTasks(nextRoundQueries);
    }
    
    currentRound++;
  }
};
```

## 🎨 UI设计方案

### 1. 自动研究控制面板
```typescript
<AutoResearchControl>
  <Button onClick={toggleConfig}>
    {showConfig ? '隐藏自动研究配置' : '开始自动研究'}
  </Button>
  
  {showConfig && (
    <AutoResearchConfig>
      <SliderInput label="搜集轮数" value={maxRounds} max={5} />
      <SliderInput label="初始查询数" value={initialQueries} max={10} />
      <SliderInput label="质量阈值" value={qualityThreshold} max={1} step={0.1} />
      <SliderInput label="最长时间(分钟)" value={maxDuration} max={60} />
      <Button onClick={startAutoResearch}>开始自动研究</Button>
    </AutoResearchConfig>
  )}
  
  {isRunning && (
    <AutoResearchProgress>
      <Progress value={currentRound} max={maxRounds} />
      <span>第{currentRound}轮，共{maxRounds}轮</span>
      <Button onClick={stopAutoResearch}>停止研究</Button>
    </AutoResearchProgress>
  )}
</AutoResearchControl>
```

### 2. 搜索结果展示增强
- **轮次标识**: `[第1轮]`、`[第2轮-反思]`、`[第2轮-评估]`
- **默认折叠**: 反思和评估内容默认折叠，点击按钮展开
- **复用组件**: 基于现有SearchResult.tsx扩展

## 📁 实现计划

### 第一阶段：核心功能 (优先级最高)
1. **数据结构扩展**
   - 修改 `src/store/task.ts` - 扩展任务状态管理
   - 修改 `src/store/setting.ts` - 添加自动研究配置
   - 新增 `src/types/auto-research.ts` - 类型定义

2. **Prompt实现**
   - 新增 `src/utils/deep-research/auto-research-prompts.ts` - 专用prompts

3. **自动研究逻辑**
   - 新增 `src/hooks/useAutoResearch.ts` - 核心逻辑
   - 修改 `src/hooks/useDeepResearch.ts` - 集成自动研究

### 第二阶段：UI集成
1. **控制组件**
   - 新增 `src/components/Research/AutoResearchControl.tsx` - 控制面板

2. **结果展示**
   - 修改 `src/components/Research/SearchResult.tsx` - 扩展显示

### 第三阶段：优化完善
1. **错误处理**: 异常情况处理
2. **性能优化**: 并发控制
3. **用户体验**: 交互反馈

## ✅ 确认要点

1. **反思/评估展示**: 默认折叠，点击按钮展开查看
2. **轮次标识**: 使用 `[第X轮]` 格式
3. **配置持久化**: 保存到localStorage  
4. **停止机制**: 完全中断，用户可刷新重新开始
5. **兼容性**: 不影响现有手动研究流程

## 🎯 成功标准

1. 用户可以在生成plan后选择开启自动研究
2. 自动研究过程中所有查询、反思、评估结果都可见
3. 用户可以配置研究参数并随时停止
4. 现有手动研究流程完全不受影响
5. 代码复用现有架构，保持技术一致性
