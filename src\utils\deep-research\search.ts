import {
  TAVILY_BASE_URL,
  FIRECRAWL_BASE_URL,
  EXA_BASE_URL,
  BOCHA_BASE_URL,
  SEARXNG_BASE_URL,
} from "@/constants/urls";
import { rewritingPrompt } from "@/constants/prompts";
import { completePath } from "@/utils/url";
import { pick, sort } from "radash";

type TavilySearchResult = {
  title: string;
  url: string;
  content: string;
  rawContent?: string;
  score: number;
  publishedDate: string;
};

interface FirecrawlDocument<T = unknown> {
  url?: string;
  markdown?: string;
  html?: string;
  rawHtml?: string;
  links?: string[];
  extract?: T;
  json?: T;
  screenshot?: string;
  compare?: {
    previousScrapeAt: string | null;
    changeStatus: "new" | "same" | "changed" | "removed";
    visibility: "visible" | "hidden";
  };
  // v1 search only
  title?: string;
  description?: string;
}

type ExaSearchResult = {
  title: string;
  url: string;
  publishedDate: string;
  author: string;
  score: number;
  id: string;
  image?: string;
  favicon: string;
  text?: string;
  highlights?: string[];
  highlightScores?: number[];
  summary?: string;
  subpages?: ExaSearchResult[];
  extras?: {
    links?: string[];
    imageLinks?: string[];
  };
};

type BochaSearchResult = {
  id: string | null;
  name: string;
  url: string;
  displayUrl: string;
  snippet: string;
  summary?: string;
  siteName: string;
  siteIcon: string;
  dateLastCrawled: string;
  cachedPageUrl: string | null;
  language: string | null;
  isFamilyFriendly: boolean | null;
  isNavigational: boolean | null;
};

type BochaImage = {
  webSearchUrl: string;
  name: string;
  thumbnailUrl: string;
  datePublished: string;
  contentUrl: string;
  hostPageUrl: string;
  contentSize: number;
  encodingFormat: string;
  hostPageDisplayUrl: string;
  width: number;
  height: number;
  thumbnail: {
    width: number;
    height: number;
  };
};

type SearxngSearchResult = {
  url: string;
  title: string;
  content?: string;
  engine: string;
  parsed_url: string[];
  template: "default.html" | "videos.html" | "images.html";
  engines: string[];
  positions: number[];
  publishedDate?: Date | null;
  thumbnail?: null | string;
  is_onion?: boolean;
  score: number;
  category: string;
  length?: null | string;
  duration?: null | string;
  iframe_src?: string;
  source?: string;
  metadata?: string;
  resolution?: null | string;
  img_src?: string;
  thumbnail_src?: string;
  img_format?: "jpeg" | "Culture Snaxx" | "png";
};

export interface SearchProviderOptions {
  provider: string;
  baseURL?: string;
  apiKey?: string;
  query: string;
  maxResult?: number;
  scope?: string;
}

// 延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 请求间隔控制 - 全局变量确保所有请求都遵守间隔
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 1500; // 1.5秒间隔

// 重试配置
const MAX_RETRIES = 3;
const RETRY_DELAYS = [2000, 5000, 10000]; // 2s, 5s, 10s

export async function createSearchProvider({
  provider,
  baseURL,
  apiKey = "",
  query,
  maxResult = 5,
  scope,
}: SearchProviderOptions) {
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };
  if (apiKey) headers.Authorization = `Bearer ${apiKey}`;

  if (provider === "tavily") {
    const requestUrl = `${completePath(baseURL || TAVILY_BASE_URL)}/search`;
    const requestBody = {
      query,
      search_depth: "advanced",
      topic: scope || "general",
      max_results: Number(maxResult),
      include_images: true,
      include_image_descriptions: true,
      include_answer: false,
      include_raw_content: true,
    };
    
    // 实现请求间隔控制
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
      const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
      console.log(`⏱️ [Tavily Rate Control] 等待 ${waitTime}ms 以避免频率限制`);
      await delay(waitTime);
    }
    lastRequestTime = Date.now();
    
    console.log(`🔍 [Tavily Request] 查询: "${query}"`);
    console.log(`🔍 [Tavily Request] URL: ${requestUrl}`);
    
    // 重试机制
    for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
      try {
        console.log(`📡 [Tavily] 尝试 ${attempt + 1}/${MAX_RETRIES + 1}: ${query.substring(0, 50)}...`);
        
        const response = await fetch(requestUrl, {
          method: "POST",
          headers,
          credentials: "omit",
          body: JSON.stringify(requestBody),
        });
        
        console.log(`📡 [Tavily Response] Status: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`❌ [Tavily Error] HTTP ${response.status}: ${errorText}`);
          
          if (response.status === 429) {
            // 429 错误 - 频率限制
            if (attempt < MAX_RETRIES) {
              const retryDelay = RETRY_DELAYS[attempt];
              console.warn(`🚫 [Tavily Rate Limit] 触发频率限制，${retryDelay/1000}秒后重试 (${attempt + 1}/${MAX_RETRIES})`);
              await delay(retryDelay);
              continue; // 重试
            } else {
              throw new Error(`Tavily API 频率限制，已重试 ${MAX_RETRIES} 次仍失败`);
            }
          } else if (response.status >= 500) {
            // 5xx 服务器错误
            if (attempt < MAX_RETRIES) {
              const retryDelay = RETRY_DELAYS[attempt];
              console.warn(`🔧 [Tavily Server Error] 服务器错误，${retryDelay/1000}秒后重试 (${attempt + 1}/${MAX_RETRIES})`);
              await delay(retryDelay);
              continue; // 重试
            } else {
              throw new Error(`Tavily API 服务器错误 (${response.status}): ${errorText}`);
            }
          } else {
            // 其他错误，不重试
            throw new Error(`Tavily API 错误 (${response.status}): ${errorText}`);
          }
        }
        
        // 请求成功
        const responseData = await response.json();
        const resultCount = responseData.results?.length || 0;
        console.log(`✅ [Tavily Success] 返回 ${resultCount} 个结果 (尝试 ${attempt + 1})`);
        
        const { results = [], images = [] } = responseData;
        return {
          sources: (results as TavilySearchResult[])
            .filter((item) => item.content && item.url)
            .map((result) => {
              return {
                title: result.title,
                content: result.rawContent || result.content,
                url: result.url,
              };
            }) as Source[],
          images: images as ImageSource[],
        };
        
      } catch (error) {
        console.error(`❌ [Tavily] 尝试 ${attempt + 1} 失败:`, error);
        
        // 如果是最后一次尝试，抛出错误
        if (attempt === MAX_RETRIES) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          throw new Error(`Tavily API 请求失败，已重试 ${MAX_RETRIES + 1} 次: ${errorMessage}`);
        }
        
        // 否则等待后重试
        const retryDelay = RETRY_DELAYS[attempt];
        console.warn(`🔄 [Tavily Retry] 网络错误，${retryDelay/1000}秒后重试 (${attempt + 1}/${MAX_RETRIES})`);
        await delay(retryDelay);
      }
    }
    
    // 理论上不会到达这里
    throw new Error("Tavily API 请求异常结束");
  } else if (provider === "firecrawl") {
    const response = await fetch(
      `${completePath(baseURL || FIRECRAWL_BASE_URL, "/v1")}/search`,
      {
        method: "POST",
        headers,
        credentials: "omit",
        body: JSON.stringify({
          query,
          limit: maxResult,
          tbs: "qdr:w",
          origin: "api",
          scrapeOptions: {
            formats: ["markdown"],
          },
          timeout: 60000,
        }),
      }
    );
    const { data = [] } = await response.json();
    return {
      sources: (data as FirecrawlDocument[])
        .filter((item) => item.description && item.url)
        .map((result) => ({
          content: result.markdown || result.description,
          url: result.url,
          title: result.title,
        })) as Source[],
      images: [],
    };
  } else if (provider === "exa") {
    const response = await fetch(
      `${completePath(baseURL || EXA_BASE_URL)}/search`,
      {
        method: "POST",
        headers,
        credentials: "omit",
        body: JSON.stringify({
          query,
          category: scope || "research paper",
          contents: {
            text: true,
            summary: {
              query: `Given the following query from the user:\n<query>${query}</query>\n\n${rewritingPrompt}`,
            },
            numResults: Number(maxResult) * 5,
            livecrawl: "auto",
            extras: {
              imageLinks: 3,
            },
          },
        }),
      }
    );
    const { results = [] } = await response.json();
    const images: ImageSource[] = [];
    return {
      sources: (results as ExaSearchResult[])
        .filter((item) => (item.summary || item.text) && item.url)
        .map((result) => {
          if (
            result.extras?.imageLinks &&
            result.extras?.imageLinks.length > 0
          ) {
            result.extras.imageLinks.forEach((url) => {
              images.push({ url, description: result.text });
            });
          }
          return {
            content: result.summary || result.text,
            url: result.url,
            title: result.title,
          };
        }) as Source[],
      images,
    };
  } else if (provider === "bocha") {
    const response = await fetch(
      `${completePath(baseURL || BOCHA_BASE_URL, "/v1")}/web-search`,
      {
        method: "POST",
        headers,
        credentials: "omit",
        body: JSON.stringify({
          query,
          freshness: "noLimit",
          summary: true,
          count: maxResult,
        }),
      }
    );
    const { data = {} } = await response.json();
    const results = data.webPages?.value || [];
    const imageResults = data.images?.value || [];
    return {
      sources: (results as BochaSearchResult[])
        .filter((item) => item.snippet && item.url)
        .map((result) => ({
          content: result.summary || result.snippet,
          url: result.url,
          title: result.name,
        })) as Source[],
      images: (imageResults as BochaImage[]).map((item) => {
        const matchingResult = (results as BochaSearchResult[]).find(
          (result) => result.url === item.hostPageUrl
        );
        return {
          url: item.contentUrl,
          description: item.name || matchingResult?.name,
        };
      }) as ImageSource[],
    };
  } else if (provider === "searxng") {
    const params = {
      q: query,
      categories:
        scope === "academic" ? ["science", "images"] : ["general", "images"],
      engines:
        scope === "academic"
          ? [
              "arxiv",
              "google scholar",
              "pubmed",
              "wikispecies",
              "google_images",
            ]
          : [
              "google",
              "bing",
              "duckduckgo",
              "brave",
              "wikipedia",
              "bing_images",
              "google_images",
            ],
      lang: "auto",
      format: "json",
      autocomplete: "google",
    };
    const searchQuery = new URLSearchParams();
    for (const [key, value] of Object.entries(params)) {
      searchQuery.append(key, value.toString());
    }
    const local = global.location || {};
    const response = await fetch(
      `${completePath(
        baseURL || SEARXNG_BASE_URL
      )}/search?${searchQuery.toString()}`,
      baseURL?.startsWith(local.origin)
        ? { method: "POST", credentials: "omit", headers }
        : { method: "GET", credentials: "omit" }
    );
    const { results = [] } = await response.json();
    const rearrangedResults = sort(
      results as SearxngSearchResult[],
      (item) => item.score,
      true
    );
    return {
      sources: rearrangedResults
        .filter((item) => item.content && item.url && item.score >= 0.5)
        .slice(0, maxResult * 5)
        .map((result) => pick(result, ["title", "content", "url"])) as Source[],
      images: rearrangedResults
        .filter((item) => item.category === "images" && item.score >= 0.5)
        .slice(0, maxResult)
        .map((result) => {
          return {
            url: result.img_src,
            description: result.title,
          };
        }) as ImageSource[],
    };
  } else {
    throw new Error("Unsupported Provider: " + provider);
  }
}
