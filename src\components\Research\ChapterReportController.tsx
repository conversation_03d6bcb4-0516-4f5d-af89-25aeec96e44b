import React, { useState } from 'react';
import { 
  FileTex<PERSON>, 
  Play, 
  Loader2, 
  CheckCircle, 
  Clock,
  AlertCircle,
  BookOpen,
  List,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useChapterResearch } from '@/hooks/useChapterResearch';
import { useTaskStore } from '@/store/task';
import { ChapterStatus } from '@/types/chapter-research';

interface ChapterReportControllerProps {
  className?: string;
}

export function ChapterReportController({ className }: ChapterReportControllerProps) {
  const taskStore = useTaskStore();
  const { 
    chapterResearch, 
    generateChapterReport, 
    generateAllChapterReports, 
    generateFinalReport 
  } = useChapterResearch();
  
  const [isGeneratingAll, setIsGeneratingAll] = useState(false);
  const [isGeneratingFinal, setIsGeneratingFinal] = useState(false);
  const [generatingChapter, setGeneratingChapter] = useState<string | null>(null);

  // 统计信息
  const completedChapters = chapterResearch.chapters.filter(c => c.status === ChapterStatus.COMPLETED);
  const existingReports = chapterResearch.chapterReports;
  const unreportedChapters = completedChapters.filter(chapter => 
    !existingReports.some(report => report.chapterId === chapter.id)
  );

  const canGenerateReports = completedChapters.length > 0;
  const canGenerateFinalReport = existingReports.length > 0;
  const allChaptersReported = unreportedChapters.length === 0 && completedChapters.length > 0;

  // 生成单个章节报告
  const handleGenerateChapterReport = async (chapterId: string) => {
    setGeneratingChapter(chapterId);
    try {
      await generateChapterReport(chapterId);
    } catch (error) {
      console.error('生成章节报告失败:', error);
    } finally {
      setGeneratingChapter(null);
    }
  };

  // 生成所有章节报告
  const handleGenerateAllReports = async () => {
    setIsGeneratingAll(true);
    try {
      await generateAllChapterReports();
    } catch (error) {
      console.error('批量生成报告失败:', error);
    } finally {
      setIsGeneratingAll(false);
    }
  };

  // 生成最终报告
  const handleGenerateFinalReport = async () => {
    setIsGeneratingFinal(true);
    try {
      await generateFinalReport();
    } catch (error) {
      console.error('生成最终报告失败:', error);
    } finally {
      setIsGeneratingFinal(false);
    }
  };

  if (!chapterResearch.isActive) {
    return null;
  }

  return (
    <Card className={`flex flex-col ${className || ''}`}>
      <CardHeader className="pb-3 flex-shrink-0">
        <CardTitle className="flex items-center gap-2 text-base">
          <FileText className="h-5 w-5 text-blue-600" />
          报告生成控制台
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4 flex-1 overflow-y-auto">
        {/* 状态概览 - 改为2列布局，更紧凑 */}
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-green-50 dark:bg-green-950/20 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <span className="text-xs text-green-600 dark:text-green-400">信息收集完成</span>
                <span className="text-lg font-bold text-green-600 dark:text-green-400">
                  {completedChapters.length}
                </span>
              </div>
            </div>
            <div className="bg-blue-50 dark:bg-blue-950/20 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <span className="text-xs text-blue-600 dark:text-blue-400">章节报告生成</span>
                <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                  {existingReports.length}
                </span>
              </div>
            </div>
          </div>
          
          {/* 最终报告状态 */}
          <div className="bg-purple-50 dark:bg-purple-950/20 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-xs text-purple-600 dark:text-purple-400">最终报告</span>
              <Badge 
                variant={taskStore.finalReport ? "default" : "outline"}
                className="text-xs"
              >
                {taskStore.finalReport ? "已生成" : "未生成"}
              </Badge>
            </div>
          </div>
        </div>

        <Separator />

        {/* 批量操作 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">批量操作</h4>
            {allChaptersReported && (
              <Badge variant="secondary" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                全部已生成
              </Badge>
            )}
          </div>

          <div className="space-y-2">
            <Button
              onClick={handleGenerateAllReports}
              disabled={!canGenerateReports || isGeneratingAll}
              size="sm"
              className="w-full"
              variant={unreportedChapters.length > 0 ? "default" : "outline"}
            >
              {isGeneratingAll ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  正在生成...
                </>
              ) : (
                <>
                  <List className="h-4 w-4 mr-2" />
                  生成所有章节报告
                  {unreportedChapters.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {unreportedChapters.length}
                    </Badge>
                  )}
                </>
              )}
            </Button>

            <Button
              onClick={handleGenerateFinalReport}
              disabled={!canGenerateFinalReport || isGeneratingFinal}
              size="sm"
              variant="outline"
              className="w-full"
            >
              {isGeneratingFinal ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  正在生成最终报告...
                </>
              ) : (
                <>
                  <BookOpen className="h-4 w-4 mr-2" />
                  生成最终报告
                </>
              )}
            </Button>
          </div>

          {unreportedChapters.length > 0 && (
            <div className="bg-orange-50 dark:bg-orange-950/20 rounded-lg p-2">
              <div className="text-xs text-orange-600 dark:text-orange-400 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1.5 flex-shrink-0" />
                还有 {unreportedChapters.length} 个章节未生成报告
              </div>
            </div>
          )}
        </div>

        {/* 单独章节控制 */}
        {completedChapters.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm">单独章节控制</h4>
                <span className="text-xs text-muted-foreground">
                  第 {completedChapters.findIndex(c => !existingReports.some(r => r.chapterId === c.id)) + 1} / {completedChapters.length} 章
                </span>
              </div>
              <div className="space-y-2 max-h-80 overflow-y-auto pr-2">
                {completedChapters.map(chapter => {
                  const hasReport = existingReports.some(r => r.chapterId === chapter.id);
                  const isGenerating = generatingChapter === chapter.id;
                  
                  return (
                    <div 
                      key={chapter.id}
                      className="flex items-center justify-between gap-2 p-2 bg-muted/30 rounded-md"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">
                          {chapter.title}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {hasReport ? (
                            <span className="text-green-600">
                              <CheckCircle className="h-3 w-3 inline mr-1" />
                              报告已生成
                            </span>
                          ) : (
                            <span className="text-orange-600">
                              <Clock className="h-3 w-3 inline mr-1" />
                              等待生成报告
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <Button
                        onClick={() => handleGenerateChapterReport(chapter.id)}
                        disabled={isGenerating}
                        size="sm"
                        variant={hasReport ? "outline" : "default"}
                        className="flex-shrink-0"
                      >
                        {isGenerating ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : hasReport ? (
                          <RefreshCw className="h-3 w-3" />
                        ) : (
                          <Play className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        )}

        {/* 提示信息 */}
        {completedChapters.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-3 opacity-30" />
            <p className="text-sm font-medium mb-2">暂无已完成的章节</p>
            <p className="text-xs text-muted-foreground/70">完成章节信息收集后即可生成报告</p>
            <div className="mt-6 p-4 bg-muted/20 rounded-lg">
              <p className="text-xs mb-2">📝 研究流程提示</p>
              <ul className="text-xs text-left space-y-1 text-muted-foreground/70">
                <li>• 等待章节完成信息收集</li>
                <li>• 点击生成按钮创建报告</li>
                <li>• 所有章节完成后生成最终报告</li>
              </ul>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}