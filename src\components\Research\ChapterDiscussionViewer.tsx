import React, { useState, useMemo, useEffect } from 'react';
import { 
  MessageCircle, 
  ChevronDown, 
  ChevronUp, 
  Brain, 
  Search, 
  Target,
  Clock,
  Lightbulb,
  CheckCircle,
  ArrowRight,
  Filter,
  Eye,
  EyeOff
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useChapterResearch } from '@/hooks/useChapterResearch';
import { AgentDiscussion, AgentId } from '@/types/chapter-research';

interface ChapterDiscussionViewerProps {
  chapterId?: string;
  showAllChapters?: boolean;
  maxHeight?: string;
}

export function ChapterDiscussionViewer({ 
  chapterId, 
  showAllChapters = false,
  maxHeight = "600px"
}: ChapterDiscussionViewerProps) {
  const { chapterResearch } = useChapterResearch();
  const [selectedChapter, setSelectedChapter] = useState<string>(chapterId || 'all');
  const [selectedRound, setSelectedRound] = useState<string>('all');
  const [expandedDiscussions, setExpandedDiscussions] = useState<Set<string>>(new Set());
  const [showThoughts, setShowThoughts] = useState(true);
  const [isClient, setIsClient] = useState(false);

  // 防止SSR水合问题
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 过滤讨论记录
  const filteredDiscussions = useMemo(() => {
    let discussions = Array.isArray(chapterResearch.discussions) ? chapterResearch.discussions : [];

    // 按章节过滤
    if (!showAllChapters && selectedChapter && selectedChapter !== 'all') {
      discussions = discussions.filter(d => d.chapterId === selectedChapter);
    } else if (showAllChapters && selectedChapter !== 'all') {
      discussions = discussions.filter(d => d.chapterId === selectedChapter);
    }

    // 按轮次过滤
    if (selectedRound !== 'all') {
      const round = parseInt(selectedRound);
      discussions = discussions.filter(d => d.roundNumber === round);
    }

    // 按时间排序
    return discussions.sort((a, b) => a.timestamp - b.timestamp);
  }, [chapterResearch.discussions, selectedChapter, selectedRound, showAllChapters]);

  // 按章节和轮次分组
  const groupedDiscussions = useMemo(() => {
    const groups: Record<string, Record<number, AgentDiscussion[]>> = {};
    
    filteredDiscussions.forEach(discussion => {
      if (!groups[discussion.chapterId]) {
        groups[discussion.chapterId] = {};
      }
      if (!groups[discussion.chapterId][discussion.roundNumber]) {
        groups[discussion.chapterId][discussion.roundNumber] = [];
      }
      groups[discussion.chapterId][discussion.roundNumber].push(discussion);
    });

    return groups;
  }, [filteredDiscussions]);

  // 获取章节信息
  const getChapterTitle = (chapterId: string) => {
    const chapters = Array.isArray(chapterResearch.chapters) ? chapterResearch.chapters : [];
    const chapter = chapters.find(c => c.id === chapterId);
    return chapter?.title || chapterId;
  };

  // 获取智能体图标和颜色
  const getAgentInfo = (agentId: AgentId) => {
    return {
      icon: agentId === 'Alpha' ? Brain : Lightbulb,
      color: agentId === 'Alpha' ? 'text-blue-600' : 'text-purple-600',
      bgColor: agentId === 'Alpha' ? 'bg-blue-50' : 'bg-purple-50',
      borderColor: agentId === 'Alpha' ? 'border-blue-200' : 'border-purple-200',
    };
  };

  // 获取动作类型信息
  const getActionInfo = (actionName: string) => {
    switch (actionName) {
      case 'HIGH_LEVEL_PROPOSE':
        return { icon: Target, label: '高层级提议', color: 'text-orange-600' };
      case 'HIGH_LEVEL_AGREE':
        return { icon: CheckCircle, label: '高层级同意', color: 'text-green-600' };
      case 'QUERY_PROPOSE':
        return { icon: Search, label: '查询提议', color: 'text-blue-600' };
      case 'EXECUTE_QUERIES':
        return { icon: ArrowRight, label: '执行查询', color: 'text-purple-600' };
      default:
        return { icon: MessageCircle, label: actionName, color: 'text-gray-600' };
    }
  };

  // 切换讨论展开状态
  const toggleDiscussion = (discussionId: string) => {
    const newExpanded = new Set(expandedDiscussions);
    if (newExpanded.has(discussionId)) {
      newExpanded.delete(discussionId);
    } else {
      newExpanded.add(discussionId);
    }
    setExpandedDiscussions(newExpanded);
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 渲染负载内容
  const renderPayload = (payload: any, actionName: string) => {
    if (!payload) return null;

    switch (actionName) {
      case 'HIGH_LEVEL_PROPOSE':
        return (
          <div className="mt-2 p-3 bg-orange-50 rounded-md border border-orange-200">
            <h5 className="font-medium text-sm text-orange-800 mb-1">提议内容</h5>
            <p className="text-sm whitespace-pre-wrap">{payload.proposal || '无具体提议'}</p>
          </div>
        );
      
      case 'HIGH_LEVEL_AGREE':
        return (
          <div className="mt-2 p-3 bg-green-50 rounded-md border border-green-200">
            <h5 className="font-medium text-sm text-green-800 mb-1">同意内容</h5>
            <p className="text-sm whitespace-pre-wrap">{payload.agreement || '同意伙伴提议'}</p>
          </div>
        );
      
      case 'QUERY_PROPOSE':
        return (
          <div className="mt-2 p-3 bg-blue-50 rounded-md border border-blue-200">
            <h5 className="font-medium text-sm text-blue-800 mb-2">查询提议</h5>
            {payload.queries && payload.queries.length > 0 ? (
              <div className="space-y-2">
                {payload.queries.map((query: any, index: number) => (
                  <div key={index} className="bg-white p-2 rounded border">
                    <p className="font-medium text-sm">{query.query}</p>
                    <p className="text-xs text-muted-foreground mt-1">{query.researchGoal}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm">无具体查询</p>
            )}
            {payload.rationale && (
              <div className="mt-2 pt-2 border-t border-blue-200">
                <p className="text-xs text-blue-700">理由: {payload.rationale}</p>
              </div>
            )}
          </div>
        );
      
      case 'EXECUTE_QUERIES':
        return (
          <div className="mt-2 p-3 bg-purple-50 rounded-md border border-purple-200">
            <h5 className="font-medium text-sm text-purple-800 mb-2">执行查询</h5>
            {payload.queries && payload.queries.length > 0 ? (
              <div className="space-y-2">
                {payload.queries.map((query: any, index: number) => (
                  <div key={index} className="bg-white p-2 rounded border">
                    <p className="font-medium text-sm">
                      <span className="text-purple-600">🔍</span> {query.query}
                    </p>
                    {query.researchGoal && (
                      <p className="text-xs text-muted-foreground mt-1">{query.researchGoal}</p>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm">执行查询中...</p>
            )}
          </div>
        );
      
      default:
        return (
          <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono">
            {JSON.stringify(payload, null, 2)}
          </div>
        );
    }
  };

  if (chapterResearch.discussions.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8 text-muted-foreground">
          <Brain className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>尚无智能体对话记录</p>
          <p className="text-sm mt-1">开始章节式研究后，对话记录将在这里显示</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold">智能体对话记录</h3>
            <Badge variant="outline">{filteredDiscussions.length} 条对话</Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowThoughts(!showThoughts)}
              className="text-xs"
            >
              {showThoughts ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
              {showThoughts ? '隐藏' : '显示'}思考过程
            </Button>
            <Filter className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>

        {/* 过滤器 */}
        {isClient && (
          <div className="flex gap-2 text-sm">
            {showAllChapters && chapterResearch.chapters.length > 1 && (
              <Select value={selectedChapter} onValueChange={setSelectedChapter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="选择章节" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有章节</SelectItem>
                  {Array.isArray(chapterResearch.chapters) && chapterResearch.chapters.map(chapter => (
                    <SelectItem key={chapter.id} value={chapter.id}>
                      {chapter.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            
            <Select value={selectedRound} onValueChange={setSelectedRound}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="选择轮次" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有轮次</SelectItem>
                {Array.from(new Set((Array.isArray(chapterResearch.discussions) ? chapterResearch.discussions : []).map(d => d.roundNumber)))
                  .sort((a, b) => a - b)
                  .map(round => (
                    <SelectItem key={round} value={round.toString()}>
                      第 {round} 轮
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea style={{ height: maxHeight }}>
          <div className="p-4 space-y-4">
            {Object.entries(groupedDiscussions).map(([chapterId, rounds]) => (
              <div key={chapterId} className="space-y-3">
                {/* 章节标题 */}
                <div className="sticky top-0 bg-white/95 backdrop-blur-sm z-10 pb-2 border-b">
                  <h4 className="font-semibold text-sm text-blue-800">
                    📚 {getChapterTitle(chapterId)}
                  </h4>
                </div>

                {/* 轮次组织 */}
                {Object.entries(rounds)
                  .sort(([a], [b]) => parseInt(a) - parseInt(b))
                  .map(([roundNumber, discussions]) => (
                  <div key={`${chapterId}-${roundNumber}`} className="space-y-2">
                    {/* 轮次标题 */}
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="h-px bg-border flex-1" />
                      <span className="bg-muted px-2 py-1 rounded text-xs font-medium">
                        第 {roundNumber} 轮对话
                      </span>
                      <div className="h-px bg-border flex-1" />
                    </div>

                    {/* 对话列表 */}
                    {discussions.map((discussion, index) => {
                      const agentInfo = getAgentInfo(discussion.agentId);
                      const actionInfo = getActionInfo(discussion.actionName);
                      const Icon = agentInfo.icon;
                      const ActionIcon = actionInfo.icon;
                      const isExpanded = expandedDiscussions.has(discussion.id);

                      return (
                        <div
                          key={`${discussion.id}-${discussion.timestamp}-${index}`}
                          className={`border rounded-lg ${agentInfo.borderColor} ${agentInfo.bgColor}`}
                        >
                          <div className="p-3">
                            {/* 对话头部 */}
                            <div className="flex items-start justify-between gap-3 mb-2">
                              <div className="flex items-center gap-2 min-w-0 flex-1">
                                <Icon className={`h-4 w-4 ${agentInfo.color} flex-shrink-0`} />
                                <span className={`font-medium text-sm ${agentInfo.color}`}>
                                  Agent {discussion.agentId}
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  <ActionIcon className="h-3 w-3 mr-1" />
                                  {actionInfo.label}
                                </Badge>
                                <span className="text-xs text-muted-foreground flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {formatTime(discussion.timestamp)}
                                </span>
                              </div>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleDiscussion(discussion.id)}
                                className="h-6 w-6 p-0 flex-shrink-0"
                              >
                                {isExpanded ? (
                                  <ChevronUp className="h-3 w-3" />
                                ) : (
                                  <ChevronDown className="h-3 w-3" />
                                )}
                              </Button>
                            </div>

                            {/* 思考过程（可选显示） */}
                            {showThoughts && discussion.thought && (
                              <div className="mb-3 p-2 bg-white/60 rounded text-sm">
                                <h5 className="font-medium text-xs text-muted-foreground mb-1">
                                  💭 思考过程
                                </h5>
                                <p className="whitespace-pre-wrap text-xs leading-relaxed">
                                  {discussion.thought}
                                </p>
                              </div>
                            )}

                            {/* 动作内容 */}
                            <Collapsible open={isExpanded}>
                              <CollapsibleContent>
                                {renderPayload(discussion.payload, discussion.actionName)}
                              </CollapsibleContent>
                            </Collapsible>
                          </div>
                          
                          {/* 连接线（除了最后一个） */}
                          {index < discussions.length - 1 && (
                            <div className="flex justify-center py-1">
                              <div className="w-px h-4 bg-border" />
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ))}
              </div>
            ))}

            {filteredDiscussions.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Filter className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>没有符合条件的对话记录</p>
                <p className="text-xs mt-1">尝试调整过滤条件</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}