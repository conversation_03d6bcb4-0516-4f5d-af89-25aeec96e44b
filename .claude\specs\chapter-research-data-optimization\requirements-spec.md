# 章节研究数据传递优化技术规范

## Problem Statement

**Business Issue**: 当前章节式研究系统在多轮信息收集过程中，智能体对话缺乏历史上下文，导致重复研究相同问题、查询效率低下，无法充分利用已收集的信息进行渐进式深化研究。

**Current State**: 
- 章节研究流程中，每轮对话的智能体只能看到当前轮次的讨论内容
- 无法访问历史达成的共识信息，导致重复讨论已解决的问题  
- 无法访问历史查询记录，导致重复执行相似的搜索
- 缺乏配置开关来控制历史信息的使用
- 四个prompt函数无法区分高层级和低层级场景的历史信息需求

**Expected Outcome**: 
- 高层级阶段智能体能够访问历史达成的共识，避免重复讨论
- 低层级阶段智能体能够访问历史查询记录，避免重复搜索
- 用户可通过开关控制是否启用历史信息功能
- 保持向后兼容性，不影响现有章节研究逻辑

## Solution Overview

**Approach**: 通过扩展ChapterInfo数据结构存储历史信息，修改prompt函数实现有条件的历史数据注入，并在前端提供配置开关来控制功能启用状态。采用渐进式添加方式确保现有功能不受影响。

**Core Changes**:
1. 扩展ChapterInfo接口增加historicalConsensus和historicalQueries数组
2. 扩展ChapterResearchConfig接口增加includeCollectedInfo布尔开关
3. 修改四个prompt函数支持有条件历史数据注入
4. 更新collectDiscussionContext函数收集和传递历史数据
5. 在ChapterResearchConfig组件增加UI开关
6. 更新数据保存逻辑实现历史数据累积

**Success Criteria**:
- 智能体能够基于历史信息做出更明智的决策
- 避免重复讨论和查询，提高研究效率
- 用户可以根据需要启用/禁用历史信息功能
- 所有现有章节研究功能保持正常工作

## Technical Implementation

### Database Changes

**Tables to Modify**: 无需修改数据库表结构（使用Zustand内存状态）

**New Tables**: 无

**Migration Scripts**: 无

### Code Changes

#### 1. 类型定义文件修改

**File**: `src/types/chapter-research.ts`

**Modifications**:
```typescript
// 扩展ChapterInfo接口
export interface ChapterInfo {
  id: string;
  title: string;
  goal: string;
  status: 'pending' | 'discussing' | 'searching' | 'writing' | 'completed';
  currentRound: number;
  currentPhase: 'high_level' | 'query' | 'searching';
  highLevelConsensus?: string;
  finalQueries?: Array<{
    query: string;
    researchGoal: string;
  }>;
  searchProgress?: SearchProgress;
  // 新增历史数据字段
  historicalConsensus?: string[]; // 历史高层级共识累积
  historicalQueries?: Array<{      // 历史查询累积  
    query: string;
    researchGoal: string;
    fromRound: number;
  }>;
}

// 扩展ChapterResearchConfig接口
export interface ChapterResearchConfig {
  maxRounds: number;
  highLevelTurns: number;
  queryTurns: number;
  autoExecuteQueries: boolean;
  enableInfoSummarization: boolean;
  // 新增配置选项
  includeCollectedInfo: boolean; // 是否在prompt中包含已收集信息
}

// 更新默认配置
export const DEFAULT_CHAPTER_RESEARCH_CONFIG: ChapterResearchConfig = {
  maxRounds: 3,
  highLevelTurns: 4,
  queryTurns: 4,
  autoExecuteQueries: true,
  enableInfoSummarization: false,
  includeCollectedInfo: true, // 默认启用
};
```

#### 2. Prompt函数修改

**File**: `src/utils/chapter-research/prompts.ts`

**Function Signature Changes**:
```typescript
// 修改四个核心prompt函数签名，增加config参数
export function generateOpeningPrompt(
  agentId: AgentId,
  chapterGoal: string,
  userQuery: string,
  allCollectedInfo: string = '',
  config?: { includeCollectedInfo?: boolean }, // 新增config参数
  historicalConsensus?: string[] // 新增历史共识参数
): string

export function generateHighLevelResponsePrompt(
  agentId: AgentId,
  chapterGoal: string,
  userQuery: string,
  currentRoundDiscussions: string,
  allCollectedInfo: string = '',
  config?: { includeCollectedInfo?: boolean }, // 新增config参数
  historicalConsensus?: string[] // 新增历史共识参数
): string

export function generateQueryStartPrompt(
  agentId: AgentId,
  chapterGoal: string,
  userQuery: string,
  highLevelConsensus: string,
  allCollectedInfo: string = '',
  config?: { includeCollectedInfo?: boolean }, // 新增config参数
  historicalQueries?: Array<{query: string; researchGoal: string; fromRound: number}> // 新增历史查询参数
): string

export function generateQueryResponsePrompt(
  agentId: AgentId,
  chapterGoal: string,
  userQuery: string,
  highLevelConsensus: string,
  currentQueryDiscussions: string,
  allCollectedInfo: string = '',
  config?: { includeCollectedInfo?: boolean }, // 新增config参数  
  historicalQueries?: Array<{query: string; researchGoal: string; fromRound: number}> // 新增历史查询参数
): string

// 更新generateAgentPrompt函数签名
export function generateAgentPrompt(
  agentId: AgentId,
  actionName: AgentActionName,
  chapterGoal: string,
  userQuery: string,
  contextData: {
    allCollectedInfo?: string;
    currentRoundDiscussions?: string;
    highLevelConsensus?: string;
    currentQueryDiscussions?: string;
    // 新增历史数据字段
    historicalConsensus?: string[];
    historicalQueries?: Array<{query: string; researchGoal: string; fromRound: number}>;
    // 新增配置字段
    config?: { includeCollectedInfo?: boolean };
  }
): string
```

**Prompt Template Modifications**:
```typescript
// generateOpeningPrompt内部逻辑修改
export function generateOpeningPrompt(
  agentId: AgentId,
  chapterGoal: string,
  userQuery: string,
  allCollectedInfo: string = '',
  config?: { includeCollectedInfo?: boolean },
  historicalConsensus?: string[]
): string {
  // 根据配置决定是否包含历史信息
  const includeInfo = config?.includeCollectedInfo !== false;
  
  // 构建历史共识信息
  let historicalInfo = '';
  if (includeInfo && historicalConsensus && historicalConsensus.length > 0) {
    historicalInfo = `

历史达成的共识：
${historicalConsensus.map((consensus, index) => `第${index + 1}轮共识: ${consensus}`).join('\n')}`;
  }

  // 构建已收集信息部分
  const collectedInfoSection = includeInfo ? `

已收集的信息：
${allCollectedInfo || "暂无信息"}` : '';

  return `# **指令：章节研究开场协议**

用户原始查询：${userQuery}

当前章节目标：${chapterGoal}${historicalInfo}${collectedInfoSection}

你需要分析上述目标，找出信息缺口。请按以下步骤思考：
// ... 后续内容保持不变
`;
}

// 类似方式修改其他三个函数...
```

#### 3. Hook函数修改

**File**: `src/hooks/useChapterResearch.ts`

**Function Modifications**:
```typescript
// 修改collectDiscussionContext函数
const collectDiscussionContext = useCallback((chapterId: string, roundNumber: number, currentTurnNumber?: number) => {
    // ... 现有逻辑保持不变
    
    const currentState = useTaskStore.getState().chapterResearch;
    const chapter = currentState.chapters.find(c => c.id === chapterId);
    
    // 收集历史数据
    const historicalConsensus = chapter?.historicalConsensus || [];
    const historicalQueries = chapter?.historicalQueries || [];
    
    // 获取配置信息
    const config = currentState.config;

    const result = {
      allCollectedInfo,
      currentRoundDiscussions: currentRoundDiscussionsText,
      previousDiscussions: previousDiscussionsText,
      highLevelConsensus,
      currentQueryDiscussions: currentQueryDiscussionsText,
      // 新增历史数据
      historicalConsensus,
      historicalQueries,
      // 新增配置信息
      config: { includeCollectedInfo: config.includeCollectedInfo },
    };
    
    return result;
}, [contextCache]);

// 修改conductAgentDiscussion函数中的prompt生成调用
const prompt = generateAgentPrompt(
  agentId,
  expectedAction,
  chapter.goal,
  currentState.userQuery,
  contextData // 现在包含历史数据和配置
);
```

#### 4. 状态管理修改

**File**: `src/store/task.ts`

**New Functions**:
```typescript
// 在TaskStore接口中新增方法
interface TaskStore {
  // ... 现有方法
  
  // 新增历史数据管理方法
  addChapterHistoricalConsensus: (chapterId: string, consensus: string) => void;
  addChapterHistoricalQueries: (chapterId: string, queries: Array<{query: string; researchGoal: string; fromRound: number}>) => void;
  clearChapterHistoricalData: (chapterId: string) => void;
}

// 实现方法
addChapterHistoricalConsensus: (chapterId: string, consensus: string) => {
  set((state) => ({
    chapterResearch: {
      ...state.chapterResearch,
      chapters: state.chapterResearch.chapters.map((chapter) =>
        chapter.id === chapterId
          ? {
              ...chapter,
              historicalConsensus: [
                ...(chapter.historicalConsensus || []),
                consensus
              ]
            }
          : chapter
      )
    }
  }));
},

addChapterHistoricalQueries: (chapterId: string, queries: Array<{query: string; researchGoal: string; fromRound: number}>) => {
  set((state) => ({
    chapterResearch: {
      ...state.chapterResearch,  
      chapters: state.chapterResearch.chapters.map((chapter) =>
        chapter.id === chapterId
          ? {
              ...chapter,
              historicalQueries: [
                ...(chapter.historicalQueries || []),
                ...queries
              ]
            }
          : chapter
      )
    }
  }));
},

clearChapterHistoricalData: (chapterId: string) => {
  set((state) => ({
    chapterResearch: {
      ...state.chapterResearch,
      chapters: state.chapterResearch.chapters.map((chapter) =>
        chapter.id === chapterId
          ? {
              ...chapter,
              historicalConsensus: [],
              historicalQueries: []
            }
          : chapter
      )
    }
  }));
}

// 修改现有的setChapterConsensus方法
setChapterConsensus: (chapterId: string, consensus: string) => {
  set((state) => {
    const updatedState = {
      chapterResearch: {
        ...state.chapterResearch,
        chapters: state.chapterResearch.chapters.map((chapter) =>
          chapter.id === chapterId
            ? { ...chapter, highLevelConsensus: consensus }
            : chapter
        )
      }
    };
    
    // 同时将共识添加到历史记录
    get().addChapterHistoricalConsensus(chapterId, consensus);
    
    return updatedState;
  });
}
```

#### 5. 前端组件修改

**File**: `src/components/Research/ChapterResearchConfig.tsx`

**UI Changes**:
```tsx
// 在基础配置部分添加新的开关
<div className="space-y-2">
  <div className="flex items-center justify-between">
    <Label htmlFor="includeCollectedInfo">使用历史信息</Label>
    <Switch
      id="includeCollectedInfo"
      checked={config.includeCollectedInfo}
      onCheckedChange={(checked) => updateConfig({ includeCollectedInfo: checked })}
      disabled={isRunning}
    />
  </div>
  <p className="text-xs text-muted-foreground">
    {config.includeCollectedInfo ? (
      <span className="text-blue-600">🧠 智能模式：使用历史共识和查询记录优化决策</span>
    ) : (
      <span className="text-gray-600">🔒 隔离模式：每轮独立思考，不使用历史信息</span>
    )}
  </p>
</div>
```

#### 6. 数据累积逻辑修改

**File**: `src/hooks/useChapterResearch.ts`

**Function Updates**:
```typescript
// 修改handleAgentAction函数，在查询执行后累积历史数据
case 'EXECUTE_QUERIES':
  // 执行搜索查询
  const queries = discussion.payload.queries || [];
  
  // 将查询添加到历史记录
  if (queries.length > 0) {
    const historicalQueries = queries.map(q => ({
      ...q,
      fromRound: roundNumber
    }));
    taskStore.addChapterHistoricalQueries(chapterId, historicalQueries);
  }
  
  // ... 现有执行逻辑
  break;
```

### API Changes

**Endpoints**: 无新增API端点

**Request/Response**: 无变化

**Validation Rules**: 无新增验证规则

### Configuration Changes

**Settings**: 
- 在ChapterResearchConfig中新增includeCollectedInfo字段
- 默认值为true，向后兼容

**Environment Variables**: 无新增环境变量

**Feature Flags**: 通过includeCollectedInfo配置项实现功能开关

## Implementation Sequence

### Phase 1: 数据结构扩展
1. **修改类型定义** - 扩展ChapterInfo和ChapterResearchConfig接口
   - 文件: `src/types/chapter-research.ts`
   - 添加historicalConsensus, historicalQueries字段到ChapterInfo
   - 添加includeCollectedInfo字段到ChapterResearchConfig
   - 更新DEFAULT_CHAPTER_RESEARCH_CONFIG

### Phase 2: 状态管理增强
2. **扩展状态管理方法** - 添加历史数据管理功能
   - 文件: `src/store/task.ts`
   - 实现addChapterHistoricalConsensus方法
   - 实现addChapterHistoricalQueries方法
   - 实现clearChapterHistoricalData方法
   - 修改setChapterConsensus方法同时保存历史

### Phase 3: Prompt系统优化
3. **修改Prompt函数** - 支持有条件历史信息注入
   - 文件: `src/utils/chapter-research/prompts.ts`
   - 为四个核心prompt函数添加config和历史数据参数
   - 实现条件逻辑：根据config.includeCollectedInfo控制历史信息显示
   - 更新generateAgentPrompt函数传递新参数

### Phase 4: 数据收集与传递
4. **更新上下文收集逻辑** - 收集和传递历史数据
   - 文件: `src/hooks/useChapterResearch.ts`
   - 修改collectDiscussionContext函数收集历史数据
   - 修改conductAgentDiscussion函数传递历史数据给prompt
   - 更新handleAgentAction函数在查询执行后累积历史数据

### Phase 5: 前端界面更新
5. **添加配置UI** - 提供用户控制开关
   - 文件: `src/components/Research/ChapterResearchConfig.tsx`
   - 在基础配置区域添加"使用历史信息"开关
   - 提供开关状态的说明文本
   - 确保运行时无法修改配置

每个阶段都应该是独立可部署和测试的，确保现有功能不受影响。

## Validation Plan

### Unit Tests
- **Prompt函数测试**: 验证历史信息条件注入逻辑
  ```typescript
  // 测试场景
  describe('generateOpeningPrompt with historical data', () => {
    it('should include historical consensus when config.includeCollectedInfo is true')
    it('should exclude historical consensus when config.includeCollectedInfo is false')
    it('should handle empty historical data gracefully')
  })
  ```

- **状态管理测试**: 验证历史数据累积和检索
  ```typescript
  describe('historical data management', () => {
    it('should accumulate consensus to historical records')
    it('should accumulate queries to historical records')
    it('should clear historical data correctly')
  })
  ```

### Integration Tests
- **完整章节研究流程**: 启用历史信息功能，验证多轮对话中的信息传递
- **配置开关测试**: 验证启用/禁用历史信息功能的行为差异
- **向后兼容性测试**: 确保现有章节研究逻辑在默认配置下正常工作

### Business Logic Verification
- **避免重复讨论**: 验证智能体能够基于历史共识避免重复高层级讨论
- **避免重复查询**: 验证智能体能够基于历史查询避免执行相似搜索
- **渐进式深化**: 验证多轮研究能够基于历史信息进行知识的渐进积累
- **配置控制**: 验证用户可以通过开关控制历史信息的使用与否

### Performance Testing
- **内存使用**: 监控历史数据累积对内存使用的影响
- **Prompt长度**: 确保历史信息不会导致prompt过长超出模型限制
- **响应时间**: 验证历史信息处理不会显著影响响应时间

## Technical Risk Mitigation

### Data Consistency
- 历史数据与当前状态的一致性通过统一的状态管理接口保证
- 使用Zustand的状态更新机制确保原子性操作

### Backward Compatibility  
- 所有新增字段都是可选的，现有代码不会受到影响
- 默认配置启用历史信息功能，保持用户体验改进
- Prompt函数向后兼容，旧的调用方式仍然有效

### Memory Management
- 历史数据仅在章节研究期间累积，章节完成后可选择清理
- 实现clearChapterHistoricalData方法提供数据清理能力
- 通过配置开关允许用户禁用历史数据功能降低内存使用

### Error Handling
- 历史数据访问失败不会影响主要研究流程
- Prompt生成时对历史数据进行空值检查和安全处理
- 提供降级机制：历史信息不可用时自动退回到现有行为