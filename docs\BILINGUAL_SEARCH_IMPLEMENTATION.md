# Deep Research 双语搜索增强功能实现文档

## 概述

本文档记录了Deep Research项目中双语搜索增强功能的完整实现过程，包括数据结构改动、功能实现和技术细节。

## 需求分析

### 原始需求
1. **默认双语检索**：改为默认中英文双语检索，使检索关键词行翻倍
2. **独立删除功能**：用户可以指定删除某个SERP查询列表及其搜集内容
3. **三种搜索模式**：支持双语、仅中文、仅英文三种模式
4. **安全删除策略**：避免在搜索过程中删除任务导致的bug

### 设计决策
- **方案A**：禁止删除正在处理中的任务（用户选择）
- **方式1**：AI生成成对的中英文查询，确保语义一致性（用户选择）

## 核心数据结构改动

### 1. SearchTask接口扩展

**文件**: `src/types.d.ts`

```typescript
// 新增搜索模式类型定义
type SearchMode = "bilingual" | "chinese" | "english";

interface SearchTask {
  state: "unprocessed" | "processing" | "completed" | "failed";
  query: string;           // 实际查询词（中文或英文）
  language: "chinese" | "english";  // 语言标识
  groupId?: string;        // 可选：用于关联中英文任务对（双语模式下使用）
  researchGoal: string;
  learning: string;        // 搜索结果内容
  sources: Source[];       // 来源链接
  images: ImageSource[];   // 图片来源
}
```

**主要变更**：
- 移除了 `englishQuery` 字段
- 添加了 `language` 字段标识查询语言
- 添加了 `groupId` 字段用于关联中英文任务对
- 每个任务现在都是独立的，有明确的语言标识

### 2. 设置存储扩展

**文件**: `src/store/setting.ts`

```typescript
interface SettingStore {
  // ... 其他字段
  queryLanguage: "chinese" | "english"; // 已废弃，保留向后兼容
  searchMode: SearchMode; // 搜索模式：双语、仅中文、仅英文
}

const defaultValues: SettingStore = {
  // ... 其他字段
  queryLanguage: "chinese", // 默认使用中文查询（已废弃，保留向后兼容）
  searchMode: "bilingual", // 默认使用双语搜索模式
};
```

## AI查询生成逻辑改动

### 1. 提示词更新

**文件**: `src/constants/prompts.ts`

```typescript
export const serpQueriesPrompt = `This is the report plan after user confirmation:
<PLAN>
{plan}
</PLAN>

Based on previous report plan, generate a list of SERP queries to further research the topic. Make sure each query is unique and not similar to each other.

**SEARCH MODE: {searchMode}**

**IMPORTANT REQUIREMENTS based on search mode:**

**If searchMode is "bilingual":**
- Generate BOTH Chinese and English versions for each query concept
- Create separate entries for Chinese and English queries
- Each query should be natural and optimized for its respective language
- Consider cultural and linguistic differences

**If searchMode is "chinese":**
- Generate only Chinese queries
- Queries should be natural and suitable for Chinese search engines
- Focus on Chinese language resources and perspectives

**If searchMode is "english":**
- Generate only English queries  
- Queries should be natural and suitable for English search engines
- Focus on English language resources and perspectives

${serpQuerySchemaPrompt}`;
```

### 2. JSON Schema更新

**文件**: `src/utils/deep-research/prompts.ts`

```typescript
export function getSERPQuerySchema() {
  return z
    .array(
      z
        .object({
          query: z.string().describe("The SERP query (查询词)."),
          language: z.enum(["chinese", "english"]).describe("Language of the query (查询语言)."),
          groupId: z.string().optional().describe("Optional group ID to associate related queries (可选的组ID用于关联相关查询)."),
          researchGoal: z
            .string()
            .describe(
              "First talk about the goal of the research that this query is meant to accomplish, then go deeper into how to advance the research once the results are found, mention additional research directions. Be as specific as possible, especially for additional research directions. JSON reserved words should be escaped."
            ),
        })
        .required({ query: true, language: true, researchGoal: true })
    )
    .describe(`List of SERP queries with language specification.`);
}
```

### 3. 函数签名更新

```typescript
export function generateSerpQueriesPrompt(plan: string, searchMode: SearchMode = "bilingual") {
  return serpQueriesPrompt
    .replace("{plan}", plan)
    .replace("{searchMode}", searchMode)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function reviewSerpQueriesPrompt(
  plan: string,
  learning: string[],
  suggestion: string,
  searchMode: SearchMode = "bilingual"
) {
  // ... 实现
}
```

## 任务存储管理改动

### 1. TaskStore函数扩展

**文件**: `src/store/task.ts`

```typescript
interface TaskFunction {
  // ... 其他函数
  removeTask: (query: string, language?: "chinese" | "english") => boolean;
  canDeleteTask: (query: string, language?: "chinese" | "english") => boolean;
}
```

### 2. 安全删除实现

```typescript
removeTask: (query, language) => {
  set((state) => ({
    tasks: state.tasks.filter((task) => {
      // 如果指定了language，则同时匹配query和language
      if (language) {
        return !(task.query === query && task.language === language);
      }
      // 如果没有指定language，则只匹配query（向后兼容）
      return task.query !== query;
    }),
  }));
  return true;
},

canDeleteTask: (query, language) => {
  const task = get().tasks.find((task) => {
    if (language) {
      return task.query === query && task.language === language;
    }
    return task.query === query;
  });
  // 只有非processing状态的任务才能删除
  return task ? task.state !== "processing" : false;
},
```

## UI组件改动

### 1. 搜索模式切换组件

**文件**: `src/components/Internal/SearchModeToggle.tsx`

新创建的组件，替代原来的 `QueryLanguageToggle`，支持三种搜索模式的切换：
- 双语搜索
- 仅中文搜索  
- 仅英文搜索

### 2. 搜索结果组件更新

**文件**: `src/components/Research/SearchResult.tsx`

主要改动：
- 更新了 `handleRetry` 函数签名，支持新的数据结构
- 更新了 `handleRemove` 函数，实现安全删除策略
- 添加了语言标识显示
- 集成了新的 `SearchModeToggle` 组件

```typescript
async function handleRetry(query: string, language: "chinese" | "english", researchGoal: string, groupId?: string) {
  const { updateTask } = useTaskStore.getState();
  const newTask: SearchTask = {
    query,
    language,
    groupId,
    researchGoal,
    learning: "",
    sources: [],
    images: [],
    state: "unprocessed",
  };
  updateTask(query, newTask);
  await runSearchTask([newTask]);
}

function handleRemove(query: string, language: "chinese" | "english") {
  const { removeTask, canDeleteTask } = useTaskStore.getState();
  
  // 检查是否可以安全删除
  if (!canDeleteTask(query, language)) {
    toast.warning(t("research.common.cannotDeleteProcessingTask", "无法删除正在处理中的任务，请等待搜索完成"));
    return;
  }
  
  removeTask(query, language);
  toast.success(t("research.common.taskDeleted", "任务已删除"));
}
```

## 搜索执行逻辑改动

### 1. useDeepResearch Hook更新

**文件**: `src/hooks/useDeepResearch.ts`

主要改动：
- 更新了 `deepResearch` 函数，传递 `searchMode` 参数
- 更新了 `reviewSearchResult` 函数，传递 `searchMode` 参数
- 简化了 `getSearchQuery` 函数，直接使用 `task.query`
- 更新了AI响应解析逻辑，支持新的数据结构

```typescript
// 将解析出的查询转换为SearchTask格式
queries = data.value.map(
  (item: { query: string; language: "chinese" | "english"; groupId?: string; researchGoal: string }) => ({
    state: "unprocessed",
    learning: "",
    sources: [],
    images: [],
    ...pick(item, ["query", "language", "groupId", "researchGoal"]),
  })
);
```

### 2. DeepResearch类更新

**文件**: `src/utils/deep-research/index.ts`

```typescript
export interface DeepResearchSearchTask {
  query: string;
  language?: "chinese" | "english"; // 可选，用于兼容
  groupId?: string; // 可选，用于兼容
  researchGoal: string;
}

// 创建SearchTask时添加language字段
const task: SearchTask = {
  query: item.query,
  language: item.language || "chinese", // 默认为中文，兼容旧数据
  groupId: item.groupId,
  researchGoal: item.researchGoal,
  state: "completed",
  learning: content,
  sources,
  images,
};
```

## 向后兼容性

### 1. 数据迁移策略
- 保留了 `queryLanguage` 设置字段，标记为已废弃但保持向后兼容
- 为旧数据提供默认值：`language: "chinese"`
- `removeTask` 函数支持不传 `language` 参数的旧调用方式

### 2. 接口兼容性
- `DeepResearchSearchTask` 接口中的新字段都是可选的
- 创建 `SearchTask` 时为缺失字段提供默认值

## 测试和验证

### 1. 构建测试
- ✅ TypeScript类型检查通过
- ✅ ESLint代码规范检查通过
- ✅ Next.js构建成功

### 2. 功能验证点
- [ ] 搜索模式切换功能
- [ ] 双语查询生成
- [ ] 独立删除功能
- [ ] 安全删除策略
- [ ] 语言标识显示

## 文件变更清单

### 核心文件修改
1. `src/types.d.ts` - 数据结构定义
2. `src/store/setting.ts` - 搜索模式设置
3. `src/store/task.ts` - 任务存储管理
4. `src/constants/prompts.ts` - AI提示词
5. `src/utils/deep-research/prompts.ts` - 查询生成逻辑
6. `src/utils/deep-research/index.ts` - DeepResearch类
7. `src/hooks/useDeepResearch.ts` - 搜索执行逻辑

### UI组件修改
8. `src/components/Internal/SearchModeToggle.tsx` - 新建模式切换组件
9. `src/components/Research/SearchResult.tsx` - 搜索结果显示

### 文件重命名
- `QueryLanguageToggle.tsx` → `SearchModeToggle.tsx`

## 下一步计划

1. **功能测试**：启动开发服务器，测试完整的双语搜索流程
2. **用户体验优化**：根据测试结果调整UI和交互
3. **性能优化**：评估双语搜索对性能的影响
4. **文档更新**：更新用户文档和API文档

---

**实现日期**: 2025-01-10  
**实现者**: Augment Agent  
**分支**: feature/bilingual-search-queries
