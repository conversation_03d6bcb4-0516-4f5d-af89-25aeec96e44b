import React, { useState } from 'react';
import { Settings, Play, Pause, RotateCcw, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useChapterResearch } from '@/hooks/useChapterResearch';
import { useTaskStore } from '@/store/task';
import { ChapterStatus } from '@/types/chapter-research';

export function ChapterResearchConfig() {
  const taskStore = useTaskStore();
  const { 
    chapterResearch, 
    isActive, 
    isRunning,
    startChapterResearch,
    startChapterDiscussion,
    pauseResearch,
    resetResearch,
    hideConfig,
    updateConfig,
  } = useChapterResearch();

  const { config, showConfig } = chapterResearch;
  
  // 研究深度状态
  const [depthMode, setDepthMode] = useState<'fast' | 'moderate' | 'deep' | 'custom'>(
    config.maxRounds === 1 ? 'fast' :
    config.maxRounds === 2 ? 'moderate' :
    config.maxRounds === 3 ? 'deep' : 'custom'
  );

  if (!showConfig) return null;

  const handleStartResearch = async () => {
    // 如果已经初始化，直接开始第一个章节的讨论
    if (chapterResearch.isActive && chapterResearch.chapters.length > 0) {
      const firstChapter = chapterResearch.chapters[0];
      if (firstChapter.status === 'pending') {
        console.log('🎯 [用户启动] 开始第一个章节的讨论:', firstChapter.title, 'ID:', firstChapter.id);
        await startChapterDiscussion(firstChapter.id);
        return;
      }
    }

    // 否则需要先初始化
    const planText = taskStore.reportPlan;
    if (!planText) {
      alert('请先生成研究计划');
      return;
    }

    const success = await startChapterResearch(planText);
    if (success) {
      // 初始化成功后，需要从最新的 store 状态获取章节信息
      // 因为 startChapterResearch 是异步的，需要确保获取到最新的状态
      await new Promise(resolve => setTimeout(resolve, 100)); // 短暂等待状态更新
      
      const updatedChapterResearch = taskStore.chapterResearch;
      if (updatedChapterResearch.chapters.length > 0) {
        const firstChapter = updatedChapterResearch.chapters[0];
        console.log('🎯 [用户启动] 初始化后开始第一个章节的讨论:', firstChapter.title, 'ID:', firstChapter.id);
        await startChapterDiscussion(firstChapter.id);
      }
    } else {
      alert('启动章节研究失败，请检查研究计划格式');
    }
  };

  return (
    <Card className="mb-6 border-blue-200 bg-blue-50/50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-lg">章节式研究配置</CardTitle>
            {isActive && (
              <Badge variant={isRunning ? "default" : "secondary"}>
                {isRunning ? '运行中' : '已配置'}
              </Badge>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={hideConfig}>
            <ChevronUp className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription>
          配置智能体对话参数和章节研究流程
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 基础配置 */}
        <div className="space-y-4">
          <h4 className="font-medium">基础配置</h4>
          
          {/* 第一行：章节研究深度和是否包含图片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 左侧：章节研究深度 */}
            <div className="space-y-3">
              <Label>章节研究深度</Label>
              <RadioGroup 
                value={depthMode} 
                onValueChange={(value: 'fast' | 'moderate' | 'deep' | 'custom') => {
                  setDepthMode(value);
                  if (value !== 'custom') {
                    const rounds = value === 'fast' ? 1 : value === 'moderate' ? 2 : 3;
                    updateConfig({ maxRounds: rounds });
                  }
                }}
                disabled={isRunning}
                className="grid grid-cols-2 gap-2"
              >
                <div className="flex items-center space-x-2 border rounded-lg p-2 cursor-pointer hover:bg-accent">
                  <RadioGroupItem value="fast" id="fast" />
                  <Label htmlFor="fast" className="cursor-pointer font-normal">快速</Label>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-2 cursor-pointer hover:bg-accent">
                  <RadioGroupItem value="moderate" id="moderate" />
                  <Label htmlFor="moderate" className="cursor-pointer font-normal">适中</Label>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-2 cursor-pointer hover:bg-accent">
                  <RadioGroupItem value="deep" id="deep" />
                  <Label htmlFor="deep" className="cursor-pointer font-normal">深度</Label>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-2 cursor-pointer hover:bg-accent">
                  <RadioGroupItem value="custom" id="custom" />
                  <Label htmlFor="custom" className="cursor-pointer font-normal">自定义</Label>
                </div>
              </RadioGroup>
              
              {/* 自定义输入框 */}
              {depthMode === 'custom' && (
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    min="4"
                    max="8"
                    value={config.maxRounds}
                    onChange={(e) => updateConfig({ maxRounds: parseInt(e.target.value) || 4 })}
                    disabled={isRunning}
                    className="w-20"
                  />
                  <span className="text-sm text-muted-foreground">轮</span>
                </div>
              )}
              
              <p className="text-xs text-muted-foreground">
                {depthMode === 'fast' && '⚡ 快速模式：每章节1轮信息收集，适合快速了解'}
                {depthMode === 'moderate' && '⚖️ 适中模式：每章节2轮信息收集，平衡速度与质量'}
                {depthMode === 'deep' && '🔍 深度模式：每章节3轮信息收集，追求全面深入'}
                {depthMode === 'custom' && '⚙️ 自定义模式：根据需求设置4-8轮信息收集'}
              </p>
            </div>
            
            {/* 右侧：包含图片 */}
            <div className="space-y-3">
              <Label>报告内容设置</Label>
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="enableImages" className="font-normal">包含图片信息</Label>
                  <Switch
                    id="enableImages"
                    checked={config.enableImages}
                    onCheckedChange={(checked) => updateConfig({ enableImages: checked })}
                    disabled={isRunning}
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  {config.enableImages ? (
                    <span className="text-green-600">🖼️ 在报告中显示相关图片</span>
                  ) : (
                    <span className="text-gray-600">📝 仅生成纯文字报告</span>
                  )}
                </p>
              </div>
            </div>
          </div>
          
          {/* 第二行：达成共识最大次数和提议查询最大次数 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="highLevelTurns">达成共识最大次数</Label>
              <Input
                id="highLevelTurns"
                type="number"
                min="2"
                max="8"
                value={config.highLevelTurns}
                onChange={(e) => updateConfig({ highLevelTurns: parseInt(e.target.value) || 4 })}
                disabled={isRunning}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">Alpha/Beta最多对话几次达成研究方向共识</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="queryTurns">提议查询最大次数</Label>
              <Input
                id="queryTurns"
                type="number"
                min="2"
                max="8"
                value={config.queryTurns}
                onChange={(e) => updateConfig({ queryTurns: parseInt(e.target.value) || 4 })}
                disabled={isRunning}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">Alpha/Beta最多对话几次确定具体搜索查询</p>
            </div>
          </div>
        </div>

        {/* 章节预览 */}
        {chapterResearch.chapters && Array.isArray(chapterResearch.chapters) && chapterResearch.chapters.length > 0 && (
          <Collapsible className="space-y-2">
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                <h4 className="font-medium">章节预览 ({chapterResearch.chapters.length}个)</h4>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-2">
              {chapterResearch.chapters.map((chapter, index) => (
                <div key={chapter.id} className="border rounded-lg p-3 bg-white">
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">第{index + 1}章</span>
                        <Badge 
                          variant={
                            chapter.status === ChapterStatus.COMPLETED ? "default" :
                            chapter.status === ChapterStatus.DISCUSSING ? "secondary" :
                            "outline"
                          }
                          className="text-xs"
                        >
                          {chapter.status === ChapterStatus.PENDING && '待开始'}
                          {chapter.status === ChapterStatus.DISCUSSING && '讨论中'}
                          {chapter.status === ChapterStatus.SEARCHING && '搜索中'}
                          {chapter.status === ChapterStatus.WRITING && '写作中'}
                          {chapter.status === ChapterStatus.COMPLETED && '已完成'}
                        </Badge>
                      </div>
                      <h5 className="font-medium text-sm mb-1 truncate" title={chapter.title}>
                        {chapter.title}
                      </h5>
                      <p className="text-xs text-muted-foreground line-clamp-2" title={chapter.goal}>
                        {chapter.goal}
                      </p>
                      {chapter.currentRound > 0 && (
                        <p className="text-xs text-blue-600 mt-1">
                          当前轮次: {chapter.currentRound} | 阶段: {
                            chapter.currentPhase === 'high_level' ? '高层级讨论' :
                            chapter.currentPhase === 'query' ? '查询讨论' : '搜索中'
                          }
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-2 pt-2 border-t">
          {!isActive ? (
            <Button onClick={handleStartResearch} className="flex-1">
              <Play className="h-4 w-4 mr-1" />
              开始章节式研究
            </Button>
          ) : (
            <>
              {isRunning ? (
                <Button onClick={pauseResearch} variant="secondary" className="flex-1">
                  <Pause className="h-4 w-4 mr-1" />
                  暂停研究
                </Button>
              ) : (
                <Button onClick={handleStartResearch} className="flex-1">
                  <Play className="h-4 w-4 mr-1" />
                  继续研究
                </Button>
              )}
              <Button onClick={resetResearch} variant="outline">
                <RotateCcw className="h-4 w-4 mr-1" />
                重置
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}