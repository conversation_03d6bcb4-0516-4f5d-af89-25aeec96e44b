# 图片描述中文翻译功能实现

## 功能概述

为Deep Research项目添加了图片描述自动翻译为中文的功能，解决了搜索API返回的英文图片描述在中文环境下的可读性问题。

## 实现方案

### 方案A：AI翻译描述（已实现）

- **实现位置**：在搜索结果获取后立即翻译图片描述
- **翻译方式**：批量翻译，提高效率
- **错误处理**：翻译失败时保留原始描述，不影响主功能
- **智能检测**：自动跳过已经是中文的描述

## 文件结构

### 新增文件

1. **`src/utils/translate.ts`** - 翻译工具函数
   - `translateImageDescriptions()` - 批量翻译图片描述
   - `translateSingleImageDescription()` - 单个描述翻译（备用）
   - `containsChinese()` - 中文检测函数

2. **`src/utils/test-search-apis.ts`** - 测试工具
   - 用于测试不同搜索API返回的数据结构
   - 验证图片描述翻译功能

3. **`IMAGE_TRANSLATION_IMPLEMENTATION.md`** - 本文档

### 修改文件

1. **`src/constants/prompts.ts`**
   - 添加 `imageDescriptionTranslatePrompt` 翻译提示词

2. **`src/utils/deep-research/index.ts`**
   - 导入翻译工具函数
   - 在搜索结果处理后添加翻译逻辑

## 核心功能

### 翻译提示词设计

```typescript
export const imageDescriptionTranslatePrompt = `请将以下英文图片描述翻译为简洁的中文，保持原意不变。如果某个描述已经是中文或为空，请保持原样。

要求：
- 翻译要简洁明了，适合作为图片说明
- 保持专业术语的准确性
- 按照输入的顺序返回翻译结果
- 如果输入为空或已经是中文，直接返回原文

输入描述列表：
{descriptions}

请按JSON格式返回翻译结果：
{"translations": ["翻译1", "翻译2", ...]}`;
```

### 翻译逻辑

1. **智能过滤**：只翻译英文描述，跳过中文和空描述
2. **批量处理**：一次API调用处理多个描述
3. **JSON解析**：确保翻译结果的准确解析
4. **错误容错**：翻译失败时使用原始描述

### 集成位置

在 `src/utils/deep-research/index.ts` 的搜索结果处理后：

```typescript
// 翻译图片描述为中文
if (images && images.length > 0) {
  try {
    const aiProvider = await this.getTaskModel();
    images = await translateImageDescriptions(images, aiProvider);
  } catch (translateError) {
    console.warn("图片描述翻译失败:", translateError);
    // 继续使用原始描述，不影响主流程
  }
}
```

## 不同搜索API的图片数据结构

### Tavily API（当前使用）
```typescript
// 请求参数
{
  include_images: true,
  include_image_descriptions: true
}

// 返回格式
{
  results: [...],
  images: [
    {
      url: "图片URL",
      description: "英文描述"
    }
  ]
}
```

### 其他API对比

- **Bocha**: 使用 `name` 字段作为描述
- **Exa**: 从 `extras.imageLinks` 提取，使用 `result.text` 作为描述
- **SearXNG**: 使用 `title` 字段作为描述
- **Firecrawl**: 不返回图片数据

## 测试方法

### 1. 调试日志测试

在 `src/utils/deep-research/search.ts` 中临时添加：

```typescript
console.log(`${provider} API 返回的图片数据:`, images);
```

### 2. 使用测试工具

```typescript
import { testSearchProviderImages } from "@/utils/test-search-apis";

// 测试特定搜索引擎
await testSearchProviderImages("tavily", "人工智能", "your-api-key");
```

### 3. 实际使用测试

1. 启动Deep Research应用
2. 进行包含图片的搜索
3. 检查图片描述是否已翻译为中文
4. 查看浏览器控制台的翻译日志

## 性能优化

1. **批量翻译**：减少API调用次数
2. **智能跳过**：避免不必要的翻译
3. **低温度设置**：确保翻译准确性（temperature: 0.3）
4. **错误容错**：不影响主要功能

## 用户体验

1. **透明处理**：用户无需额外操作
2. **渐进增强**：翻译失败时仍可使用原始描述
3. **性能友好**：不显著增加搜索时间
4. **多语言支持**：自动检测和处理中英文混合内容

## 未来扩展

1. **缓存机制**：避免重复翻译相同内容
2. **用户设置**：允许用户选择是否启用翻译
3. **双语显示**：同时显示原文和译文
4. **翻译质量**：添加翻译质量评估和优化

## 注意事项

1. **API成本**：翻译功能会增加AI API的使用量
2. **网络延迟**：可能略微增加搜索响应时间
3. **翻译准确性**：依赖于AI模型的翻译质量
4. **错误处理**：确保翻译失败不影响主功能

## 配置要求

- 需要配置可用的AI provider（OpenAI、Anthropic等）
- 确保AI模型支持中英文翻译
- 建议使用较新的模型以获得更好的翻译质量
