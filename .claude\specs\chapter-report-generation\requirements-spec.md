# 章节报告图像问题修复、呈现以及最终报告功能 - 技术实现规范

## 问题陈述

**业务问题**: 当前章节研究系统存在以下核心问题：
1. executeSearchQueries函数中图片资源丢失，未能将搜索到的图片传递到章节收集信息中
2. AI总结缺乏标准化的引用格式，不能复用现有的citation规则
3. 章节报告生成缺乏标准化模板和引用格式
4. 缺乏章节报告的UI展示组件
5. 没有将多个章节报告整合为最终报告的功能

**当前状态**: 
- executeSearchQueries能正确获取搜索结果的sources和images，但在创建ChapterCollectedInfo时只保留了sources
- generateSearchResultSummary使用的是通用的processSearchResultPrompt，不包含citation规则
- generateChapterReport使用简单的prompt，没有复用现有的citation和image prompts
- ChapterCollectedInfo类型定义缺少图片字段
- 没有专门的章节报告展示UI组件

**期望结果**: 
1. 修复图片数据流，确保搜索到的图片正确传递到章节收集信息和报告中
2. 标准化AI总结的引用格式，确保总结包含正确的引用标记
3. 创建标准化的章节报告生成prompt，复用现有的citation和image格式规则
4. 实现章节报告展示UI，支持markdown渲染和图片lightbox
5. 实现多章节报告整合功能，生成统一的最终报告

## 解决方案概述

**方法**: 通过四个核心修复点实现完整的章节报告功能链路：
1. **数据流修复** - 修复executeSearchQueries中图片传递逻辑，扩展ChapterCollectedInfo类型支持图片
2. **AI总结优化** - 使用citationRulesPrompt确保搜索结果总结包含正确引用格式
3. **报告生成标准化** - 创建标准化章节报告生成prompt，复用现有citation和image prompts
4. **UI展示和最终整合** - 创建章节报告展示组件，实现最终报告整合功能

**核心变更**:
- 修复useChapterResearch.ts中的executeSearchQueries函数
- 扩展types/chapter-research.ts中的ChapterCollectedInfo接口
- 优化generateSearchResultSummary的prompt使用
- 创建新的章节报告生成prompt
- 新建ChapterReportsViewer UI组件
- 实现最终报告整合逻辑

**成功标准**:
1. 图片在整个章节研究流程中正确传递和展示
2. AI总结包含正确的引用标记格式
3. 章节报告符合现有的citation和image格式标准
4. UI组件支持折叠展示和图片lightbox功能
5. 最终报告成功整合所有章节内容，处理引用去重

## 技术实现

### 数据结构变更

**扩展ChapterCollectedInfo类型**:
```typescript
// 文件: src/types/chapter-research.ts
export interface ChapterCollectedInfo {
  chapterId: string;
  roundNumber: number;
  searchResults: Array<{
    query: string;
    researchGoal: string;
    results: string;          // 搜索结果内容
    sources: Source[];        // 来源信息
    images: ImageSource[];    // 新增：图片来源
  }>;
  summary: string;            // 本轮收集信息的总结
  // 新增：汇总的图片信息，用于后续报告生成
  aggregatedImages: ImageSource[];
}
```

**扩展ChapterReport类型**:
```typescript
// 文件: src/types/chapter-research.ts
export interface ChapterReport {
  chapterId: string;
  title: string;
  content: string;           // 章节报告内容
  sources: Source[];         // 引用来源
  images: ImageSource[];     // 新增：报告中使用的图片
  generatedAt: number;       // 生成时间
}
```

### 代码变更

**修复executeSearchQueries中的图片传递**:
```typescript
// 文件: src/hooks/useChapterResearch.ts
// 修改位置: executeSearchQueries函数，第451-458行

// 修改前：
const searchResultEntry = {
  query: queryText,
  researchGoal: researchGoal,
  results: '', // 将在后面填充
  sources: searchResult.sources,
};

// 修改后：
const searchResultEntry = {
  query: queryText,
  researchGoal: researchGoal,
  results: '', // 将在后面填充
  sources: searchResult.sources,
  images: searchResult.images || [], // 新增：保留图片信息
};
```

**修改ChapterCollectedInfo构建逻辑**:
```typescript
// 文件: src/hooks/useChapterResearch.ts
// 修改位置: executeSearchQueries函数，第525-531行

// 修改前：
const collectedInfo: ChapterCollectedInfo = {
  chapterId,
  roundNumber,
  searchResults,
  summary: `第${roundNumber}轮收集到${searchResults.length}个查询的信息${enableInfoSummarization ? '（已总结）' : '（原始内容）'}`,
};

// 修改后：
// 汇总所有搜索结果中的图片
const aggregatedImages = searchResults.reduce((acc, result) => {
  return acc.concat(result.images || []);
}, [] as ImageSource[]);

const collectedInfo: ChapterCollectedInfo = {
  chapterId,
  roundNumber,
  searchResults,
  summary: `第${roundNumber}轮收集到${searchResults.length}个查询的信息${enableInfoSummarization ? '（已总结）' : '（原始内容）'}`,
  aggregatedImages, // 新增：汇总的图片信息
};
```

**优化generateSearchResultSummary使用citation规则**:
```typescript
// 文件: src/hooks/useChapterResearch.ts
// 修改位置: generateSearchResultSummary函数，第376-396行

// 需要导入citationRulesPrompt
import { citationRulesPrompt } from '@/constants/prompts';

// 修改generateSearchResultSummary函数：
const generateSearchResultSummary = useCallback(async (
  query: string,
  researchGoal: string,
  sources: Source[]
): Promise<string> => {
  console.log(`📝 [搜索总结] 开始总结搜索结果: "${query}"`);
  
  try {
    const model = await createModelProvider(getModel().networkingModel);
    const result = await generateText({
      model,
      system: getSystemPrompt(),
      // 修改：使用带citation规则的prompt
      prompt: processSearchResultPrompt(query, researchGoal, sources, false) + `\n\n${citationRulesPrompt}`,
      temperature: 0.6,
    });
    
    console.log(`✅ [搜索总结] 总结完成，长度: ${result.text.length}`);
    return result.text;
  } catch (error) {
    console.error(`❌ [搜索总结] 总结失败:`, error);
    // 如果总结失败，返回原始内容
    return sources.map(s => s.content).join('\n\n');
  }
}, [createModelProvider, getModel]);
```

**创建标准化章节报告生成prompt**:
```typescript
// 新增文件: src/utils/chapter-research/report-prompts.ts

import { 
  citationRulesPrompt, 
  finalReportCitationImagePrompt, 
  finalReportReferencesPrompt 
} from '@/constants/prompts';

export const generateChapterReportPrompt = (
  chapterTitle: string,
  chapterGoal: string,
  collectedInfo: string,
  sources: Source[],
  images: ImageSource[],
  enableReferences: boolean = true,
  enableImages: boolean = true
): string => {
  const imageSection = enableImages && images.length > 0 
    ? `\n\n**可用图片资源:**\n${images.map(img => `- ![${img.alt || ''}](${img.url})${img.chineseDescription ? ` (${img.chineseDescription})` : ''}`).join('\n')}\n\n${finalReportCitationImagePrompt}` 
    : '';
  
  const referencesSection = enableReferences && sources.length > 0 
    ? `\n\n${finalReportReferencesPrompt}` 
    : '';

  return `
基于以下信息，为"${chapterTitle}"章节写一份详细的研究报告：

章节目标: ${chapterGoal}

收集的信息:
${collectedInfo}

要求：
1. 围绕章节目标组织内容
2. 使用收集到的信息支撑观点
3. 结构清晰，逻辑连贯
4. 使用中文写作
5. 包含必要的引用说明${enableReferences ? '和引用列表' : ''}${enableImages ? '和相关图片' : ''}

${enableReferences ? citationRulesPrompt : ''}${imageSection}${referencesSection}

请生成一份完整的章节报告。
  `;
};
```

**修改generateChapterReport函数**:
```typescript
// 文件: src/hooks/useChapterResearch.ts
// 修改位置: generateChapterReport函数，第649-707行

// 需要导入新的prompt工具
import { generateChapterReportPrompt } from '@/utils/chapter-research/report-prompts';

// 修改generateChapterReport函数：
const generateChapterReport = useCallback(async (chapterId: string) => {
  console.log(`📝 [章节报告] 开始生成章节报告: ${chapterId}`);
  
  const currentState = taskStore.chapterResearch;
  const chapter = currentState.chapters.find(c => c.id === chapterId);
  if (!chapter) {
    throw new Error(`找不到章节: ${chapterId}`);
  }

  try {
    // 获取该章节的所有收集信息
    const allInfo = getChapterCollectedSummary(chapterId, currentState.collectedInfo);
    
    // 收集所有sources和images
    const allSources: Source[] = [];
    const allImages: ImageSource[] = [];
    
    currentState.collectedInfo
      .filter(info => info.chapterId === chapterId)
      .forEach(info => {
        info.searchResults.forEach(result => {
          allSources.push(...result.sources);
          if (result.images) {
            allImages.push(...result.images);
          }
        });
        // 添加汇总的图片
        if (info.aggregatedImages) {
          allImages.push(...info.aggregatedImages);
        }
      });

    // 去重处理
    const uniqueSources = allSources.filter((source, index, self) => 
      index === self.findIndex(s => s.url === source.url)
    );
    const uniqueImages = allImages.filter((image, index, self) => 
      index === self.findIndex(img => img.url === image.url)
    );

    // 使用标准化的报告生成prompt
    const reportPrompt = generateChapterReportPrompt(
      chapter.title,
      chapter.goal,
      allInfo,
      uniqueSources,
      uniqueImages,
      true, // enableReferences
      true  // enableImages
    );

    const model = await createModelProvider(getModel().thinkingModel);
    const result = await generateText({
      model,
      prompt: reportPrompt,
      temperature: 0.6,
    });
    const reportContent = result.text;

    // 保存章节报告
    const chapterReport: ChapterReport = {
      chapterId,
      title: chapter.title,
      content: reportContent,
      sources: uniqueSources,
      images: uniqueImages, // 新增：保存使用的图片
      generatedAt: Date.now(),
    };

    taskStore.addChapterReport(chapterReport);
    console.log(`✅ [章节报告] 章节报告生成完成: ${chapter.title}`);
    
    return chapterReport;

  } catch (error) {
    console.error('❌ [章节报告] 生成报告失败:', error);
    throw error;
  }
}, [taskStore, createModelProvider, getModel]);
```

### 新增组件

**创建章节报告展示组件**:
```typescript
// 新增文件: src/components/Research/ChapterReportsViewer.tsx

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Eye, Download, FileText } from 'lucide-react';
import { useTaskStore } from '@/store/task';
import { ChapterReport } from '@/types/chapter-research';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// 图片Lightbox组件
const ImageLightbox: React.FC<{
  src: string;
  alt: string;
  isOpen: boolean;
  onClose: () => void;
}> = ({ src, alt, isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div className="relative max-w-4xl max-h-full">
        <img 
          src={src} 
          alt={alt}
          className="max-w-full max-h-full object-contain"
          onClick={(e) => e.stopPropagation()}
        />
        <Button
          variant="secondary"
          size="sm"
          className="absolute top-2 right-2"
          onClick={onClose}
        >
          关闭
        </Button>
      </div>
    </div>
  );
};

// 自定义Markdown渲染器，支持图片lightbox
const MarkdownRenderer: React.FC<{ content: string }> = ({ content }) => {
  const [lightboxImage, setLightboxImage] = useState<{src: string, alt: string} | null>(null);

  const handleImageClick = (src: string, alt: string) => {
    setLightboxImage({ src, alt });
  };

  return (
    <>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          img: ({ src, alt }) => (
            <img 
              src={src} 
              alt={alt || ''} 
              className="max-w-full h-auto cursor-pointer hover:opacity-80 transition-opacity"
              onClick={() => handleImageClick(src || '', alt || '')}
            />
          )
        }}
      >
        {content}
      </ReactMarkdown>
      <ImageLightbox
        src={lightboxImage?.src || ''}
        alt={lightboxImage?.alt || ''}
        isOpen={!!lightboxImage}
        onClose={() => setLightboxImage(null)}
      />
    </>
  );
};

// 单个章节报告卡片组件
const ChapterReportCard: React.FC<{ 
  report: ChapterReport; 
  chapterIndex: number;
}> = ({ report, chapterIndex }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const handleExport = () => {
    const blob = new Blob([report.content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${report.title}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Card className="w-full">
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CardHeader className="pb-3">
          <CollapsibleTrigger asChild>
            <div className="flex items-center justify-between cursor-pointer">
              <div className="flex items-center gap-2">
                {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                <CardTitle className="text-lg">
                  第{chapterIndex + 1}章：{report.title}
                </CardTitle>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {report.sources.length} 个来源
                </Badge>
                <Badge variant="outline">
                  {report.images.length} 张图片
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleExport();
                  }}
                >
                  <Download className="h-4 w-4 mr-1" />
                  导出
                </Button>
              </div>
            </div>
          </CollapsibleTrigger>
        </CardHeader>
        <CollapsibleContent>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              <MarkdownRenderer content={report.content} />
            </div>
            
            {report.sources.length > 0 && (
              <div className="mt-6 pt-4 border-t">
                <h4 className="font-medium text-sm text-muted-foreground mb-2">
                  参考来源 ({report.sources.length})
                </h4>
                <div className="space-y-1">
                  {report.sources.map((source, index) => (
                    <div key={index} className="text-xs text-muted-foreground">
                      [{index + 1}]: 
                      <a 
                        href={source.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="ml-1 text-blue-600 hover:text-blue-800"
                      >
                        {source.title || source.url}
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

// 主要的章节报告查看器组件
export const ChapterReportsViewer: React.FC = () => {
  const taskStore = useTaskStore();
  const { chapterResearch } = taskStore;

  // 按章节顺序排序报告
  const sortedReports = useMemo(() => {
    return chapterResearch.chapterReports
      .slice()
      .sort((a, b) => {
        const aIndex = chapterResearch.chapters.findIndex(c => c.id === a.chapterId);
        const bIndex = chapterResearch.chapters.findIndex(c => c.id === b.chapterId);
        return aIndex - bIndex;
      });
  }, [chapterResearch.chapterReports, chapterResearch.chapters]);

  const handleExportAll = () => {
    const combinedContent = sortedReports
      .map((report, index) => `# 第${index + 1}章：${report.title}\n\n${report.content}`)
      .join('\n\n---\n\n');

    const blob = new Blob([combinedContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '章节报告汇总.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (sortedReports.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>暂无章节报告</p>
            <p className="text-sm mt-1">完成章节信息收集后，可生成章节报告</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">章节报告</h3>
          <p className="text-sm text-muted-foreground">
            共 {sortedReports.length} 个章节报告
          </p>
        </div>
        <Button onClick={handleExportAll} variant="outline">
          <Download className="h-4 w-4 mr-2" />
          导出全部
        </Button>
      </div>
      
      <div className="space-y-3">
        {sortedReports.map((report, index) => (
          <ChapterReportCard
            key={report.chapterId}
            report={report}
            chapterIndex={index}
          />
        ))}
      </div>
    </div>
  );
};

export default ChapterReportsViewer;
```

### API变更

**修改generateFinalReport函数**:
```typescript
// 文件: src/hooks/useChapterResearch.ts
// 修改位置: generateFinalReport函数，第732-786行

// 需要导入相关prompt
import { 
  finalReportCitationImagePrompt, 
  finalReportReferencesPrompt 
} from '@/constants/prompts';

// 修改generateFinalReport函数：
const generateFinalReport = useCallback(async () => {
  console.log('📊 [最终报告] 开始汇总所有章节报告');

  try {
    const currentState = taskStore.chapterResearch;
    const allReports = currentState.chapterReports
      .sort((a, b) => {
        // 按章节顺序排序
        const aIndex = currentState.chapters.findIndex(c => c.id === a.chapterId);
        const bIndex = currentState.chapters.findIndex(c => c.id === b.chapterId);
        return aIndex - bIndex;
      });

    // 收集所有sources和images，进行去重处理
    const allSources: Source[] = [];
    const allImages: ImageSource[] = [];
    
    allReports.forEach(report => {
      allSources.push(...report.sources);
      allImages.push(...report.images);
    });

    // 去重处理
    const uniqueSources = allSources.filter((source, index, self) => 
      index === self.findIndex(s => s.url === source.url)
    );
    const uniqueImages = allImages.filter((image, index, self) => 
      index === self.findIndex(img => img.url === image.url)
    );

    const combinedContent = allReports
      .map(report => `# ${report.title}\n\n${report.content}`)
      .join('\n\n---\n\n');

    // 构建可用图片信息
    const imageSection = uniqueImages.length > 0 
      ? `\n\n**可用图片资源:**\n${uniqueImages.map(img => `- ![${img.alt || ''}](${img.url})${img.chineseDescription ? ` (${img.chineseDescription})` : ''}`).join('\n')}\n\n${finalReportCitationImagePrompt}` 
      : '';

    const finalPrompt = `
请将以下各章节报告汇总并润色为一份完整的研究报告：

用户原始查询: ${currentState.userQuery}

各章节内容:
${combinedContent}

要求：
1. 添加引言和结论
2. 确保各章节间的衔接自然
3. 统一写作风格和术语使用
4. 突出重点发现和洞察
5. 使用中文写作
6. 整合和去重引用来源
7. 合理使用图片资源

${finalReportReferencesPrompt}${imageSection}

请生成最终的完整报告。
    `;

    const model = await createModelProvider(getModel().thinkingModel);
    const result = await generateText({
      model,
      prompt: finalPrompt,
      temperature: 0.5,
    });
    const finalReport = result.text;

    // 更新最终报告
    taskStore.updateFinalReport(finalReport);
    taskStore.setChapterResearchRunning(false);

    console.log('✅ [最终报告] 章节式研究完成');

  } catch (error) {
    console.error('❌ [最终报告] 生成失败:', error);
    taskStore.setChapterResearchRunning(false);
    throw error;
  }
}, [taskStore, createModelProvider, getModel]);
```

### 配置变更

**无需新增环境变量**

## 实现序列

### 第1阶段：数据流修复
1. **修改ChapterCollectedInfo类型** - 在types/chapter-research.ts中扩展接口，添加图片字段支持
2. **修复executeSearchQueries函数** - 在useChapterResearch.ts中修复图片传递逻辑
3. **测试图片数据流** - 验证搜索到的图片正确传递到章节收集信息

### 第2阶段：AI总结优化
1. **修改generateSearchResultSummary函数** - 添加citationRulesPrompt到总结过程
2. **创建报告生成prompt工具** - 新建utils/chapter-research/report-prompts.ts
3. **测试AI总结引用格式** - 验证总结包含正确的引用标记

### 第3阶段：章节报告生成优化
1. **修改generateChapterReport函数** - 使用标准化的报告生成prompt
2. **扩展ChapterReport类型** - 添加图片字段支持
3. **测试章节报告生成** - 验证报告包含正确的引用和图片

### 第4阶段：UI展示和最终整合
1. **创建ChapterReportsViewer组件** - 实现折叠卡片式展示和图片lightbox
2. **修改generateFinalReport函数** - 实现多章节报告整合和引用去重
3. **集成UI组件到主界面** - 在适当位置添加章节报告展示功能

每个阶段都是独立可部署和测试的。

## 验证计划

**单元测试**:
- 测试executeSearchQueries正确传递图片数据
- 测试generateSearchResultSummary包含引用格式
- 测试generateChapterReport使用标准化prompt
- 测试ChapterReportsViewer组件渲染和交互功能

**集成测试**:
- 端到端章节研究流程，验证图片在整个链路中的传递
- 多章节报告生成和最终整合功能
- UI组件与后端数据的集成测试

**业务逻辑验证**:
- 验证修复后的系统解决了原有的图片丢失问题
- 验证AI总结和章节报告符合现有的citation和image格式标准
- 验证UI提供了良好的用户体验，支持报告查看和导出功能