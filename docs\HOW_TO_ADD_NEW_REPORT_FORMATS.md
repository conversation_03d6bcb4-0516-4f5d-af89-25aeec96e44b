# 如何添加新的报告格式

## 概述

本文档详细说明如何在 Deep Research 项目中添加新的报告格式。系统采用分层prompt架构，使得添加新格式变得简单和灵活。

## 添加方式

### 方式一：添加预定义报告格式（推荐）

适用于：常用的、标准化的报告格式

#### 步骤1：更新类型定义

编辑 `src/types/report.ts`：

```typescript
// 1. 更新 ReportType 类型
export type ReportType = 'standard' | 'collateral-analysis' | 'custom' | 'your-new-format';

// 2. 在 REPORT_CONFIGS 中添加新格式配置
export const REPORT_CONFIGS: Record<ReportType, ReportConfig> = {
  // ... 现有配置
  'your-new-format': {
    type: 'your-new-format',
    planWeight: 'weak', // 或 'strong' / 'none'
    name: 'Your New Format',
    description: 'Description of your new format',
    icon: '🆕' // 选择合适的emoji图标
  }
};
```

#### 步骤2：定义格式指导Prompt

编辑 `src/utils/deep-research/report-formats.ts`：

```typescript
const formatInstructions = {
  // ... 现有格式
  'your-new-format': `Write a report in your new format with the following structure:

## **Section 1: Title**
- Specific requirements for this section
- Expected content type and style

## **Section 2: Title**  
- Specific requirements for this section
- Expected content type and style

## **Section 3: Title**
- Specific requirements for this section
- Expected content type and style

**Additional Requirements:**
- Use [specific tone/style]
- Target audience: [audience description]
- Length: [expected length]
- Special formatting: [any special requirements]

**Key Guidelines:**
- Ensure [specific requirement 1]
- Focus on [specific requirement 2]
- Include [specific elements]`
};
```

#### 步骤3：添加国际化文本

编辑 `src/locales/zh-CN.json`：

```json
{
  "research": {
    "finalReport": {
      "reportTypes": {
        "your-new-format": "您的新格式名称"
      },
      "reportTypeDescriptions": {
        "your-new-format": "您的新格式的详细描述，说明适用场景和特点"
      }
    }
  }
}
```

编辑 `src/locales/en-US.json`：

```json
{
  "research": {
    "finalReport": {
      "reportTypes": {
        "your-new-format": "Your New Format Name"
      },
      "reportTypeDescriptions": {
        "your-new-format": "Detailed description of your new format, explaining use cases and features"
      }
    }
  }
}
```

#### 步骤4：更新表单验证

编辑 `src/components/Research/FinalReport/index.tsx`：

```typescript
const formSchema = z.object({
  requirement: z.string().optional(),
  reportType: z.enum(['standard', 'collateral-analysis', 'custom', 'your-new-format']),
  customPrompt: z.string().optional(),
  selectedCustomFormatId: z.string().nullable().optional(),
});
```

### 方式二：添加预设自定义格式模板

适用于：特定用途的格式，不需要成为核心格式

编辑 `src/types/report.ts` 中的 `DEFAULT_CUSTOM_FORMATS`：

```typescript
export const DEFAULT_CUSTOM_FORMATS: CustomReportFormat[] = [
  // ... 现有模板
  {
    id: 'your-template-id',
    name: 'Your Template Name',
    description: 'Description of your template',
    prompt: `Your detailed prompt instructions here...

## **Structure Example:**
### **Part 1: Introduction**
- Brief overview of the topic
- Context and background

### **Part 2: Analysis**  
- Detailed analysis based on research data
- Key insights and findings

### **Part 3: Conclusion**
- Summary of main points
- Recommendations and next steps

**Style Requirements:**
- Use professional tone
- Include data-driven insights
- Target length: 3-5 pages`,
    planWeight: 'weak', // 或 'strong' / 'none'
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];
```

## Prompt编写最佳实践

### 1. 结构化设计

```typescript
const promptTemplate = `Write a [FORMAT_TYPE] report with the following structure:

## **Main Sections**
### **Section 1: [SECTION_NAME]**
- **Purpose**: [What this section should achieve]
- **Content**: [What should be included]
- **Style**: [How it should be written]
- **Length**: [Expected length]

### **Section 2: [SECTION_NAME]**
- **Purpose**: [What this section should achieve]
- **Content**: [What should be included]
- **Style**: [How it should be written]
- **Length**: [Expected length]

## **Overall Requirements**
- **Tone**: [Professional/Academic/Casual/etc.]
- **Audience**: [Target readers]
- **Length**: [Total expected length]
- **Special Features**: [Citations/Tables/Charts/etc.]

## **Quality Standards**
- Ensure accuracy and factual correctness
- Maintain logical flow and coherence
- Use clear and concise language
- Include relevant data and evidence`;
```

### 2. Plan权重选择指南

- **Strong**: 严格遵循原始研究计划
  - 适用于：需要保持原有结构的格式
  - 示例：标准综合报告

- **Weak**: 参考原计划但优先新格式
  - 适用于：有特定格式要求但仍需研究数据的报告
  - 示例：执行摘要、学术论文

- **None**: 完全忽略原计划
  - 适用于：完全不同的格式要求
  - 示例：创意写作、故事叙述

### 3. 常用格式模板

#### 商业报告格式
```typescript
`Write a business report with the following structure:

## **Executive Summary**
- Key findings in 2-3 paragraphs
- Main recommendations
- Critical success factors

## **Market Analysis**
- Current market situation
- Competitive landscape
- Opportunities and threats

## **Financial Analysis**
- Revenue projections
- Cost analysis
- ROI calculations

## **Strategic Recommendations**
- Short-term actions (0-6 months)
- Medium-term strategy (6-18 months)
- Long-term vision (18+ months)

## **Implementation Plan**
- Timeline and milestones
- Resource requirements
- Risk mitigation strategies

Use professional business language with data-driven insights.`
```

#### 技术文档格式
```typescript
`Write a technical documentation report with the following structure:

## **Overview**
- System/technology summary
- Purpose and objectives
- Scope and limitations

## **Technical Specifications**
- Architecture overview
- Key components and features
- Performance metrics

## **Implementation Details**
- Setup and configuration
- Integration requirements
- Best practices

## **Use Cases and Examples**
- Common scenarios
- Code examples (if applicable)
- Troubleshooting guide

## **Conclusion**
- Summary of benefits
- Future considerations
- Maintenance requirements

Use clear, technical language with practical examples.`
```

#### 研究报告格式
```typescript
`Write a research report with the following structure:

## **Abstract**
- Research question and objectives
- Methodology summary
- Key findings and conclusions

## **Introduction**
- Background and context
- Literature review
- Research hypothesis

## **Methodology**
- Data collection methods
- Analysis techniques
- Limitations and constraints

## **Results**
- Findings presentation
- Data analysis and interpretation
- Statistical significance

## **Discussion**
- Implications of findings
- Comparison with existing research
- Practical applications

## **Conclusion**
- Summary of contributions
- Future research directions
- Recommendations

Use academic writing style with proper citations and evidence-based conclusions.`
```

## 测试新格式

### 1. 功能测试
```bash
# 启动开发服务器
npm run dev

# 测试步骤：
# 1. 完成一个研究任务
# 2. 在最终报告区域选择新格式
# 3. 生成报告并检查输出
# 4. 验证格式是否符合预期
```

### 2. 验证清单
- [ ] 格式在下拉菜单中正确显示
- [ ] 格式描述准确且有帮助
- [ ] 生成的报告符合预期结构
- [ ] 国际化文本正确显示
- [ ] Plan权重设置生效
- [ ] 报告质量满足要求

## 高级定制

### 1. 动态格式选择
可以根据研究内容动态推荐格式：

```typescript
function recommendFormat(researchData: any): ReportType {
  // 根据研究数据特征推荐格式
  if (researchData.hasFinancialData) {
    return 'collateral-analysis';
  }
  if (researchData.isAcademicTopic) {
    return 'academic-paper';
  }
  return 'standard';
}
```

### 2. 条件格式
根据不同条件使用不同的prompt变体：

```typescript
function getConditionalPrompt(reportType: string, context: any): string {
  const basePrompt = formatInstructions[reportType];
  
  if (context.hasImages) {
    return basePrompt + '\n\n**Image Integration**: Include relevant images with detailed captions.';
  }
  
  if (context.hasMultipleSources) {
    return basePrompt + '\n\n**Source Integration**: Synthesize information from multiple sources effectively.';
  }
  
  return basePrompt;
}
```

### 3. 格式组合
支持多个格式的组合使用：

```typescript
function combineFormats(primaryFormat: string, secondaryFormat: string): string {
  const primary = formatInstructions[primaryFormat];
  const secondary = formatInstructions[secondaryFormat];
  
  return `${primary}\n\n**Additional Requirements from ${secondaryFormat}:**\n${secondary}`;
}
```

## 维护和更新

### 1. 格式版本管理
为格式添加版本控制：

```typescript
interface FormatVersion {
  version: string;
  prompt: string;
  changelog: string;
  deprecated?: boolean;
}
```

### 2. 用户反馈集成
收集用户对新格式的反馈：

```typescript
interface FormatFeedback {
  formatId: string;
  rating: number;
  comments: string;
  suggestions: string;
}
```

### 3. A/B测试
对不同版本的格式进行测试：

```typescript
function getFormatVariant(formatId: string, userId: string): string {
  // 根据用户ID返回不同的格式变体
  const variants = formatVariants[formatId];
  const variantIndex = hashUserId(userId) % variants.length;
  return variants[variantIndex];
}
```

## 总结

添加新的报告格式主要涉及四个步骤：
1. 定义格式类型和配置
2. 编写格式指导prompt
3. 添加国际化文本
4. 测试验证功能

通过遵循这些步骤和最佳实践，您可以轻松地扩展 Deep Research 的报告格式功能，满足不同用户的需求。
