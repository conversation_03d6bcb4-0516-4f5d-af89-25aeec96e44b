# Deep Research 图片中文说明功能实现文档

## 功能概述

为 Deep Research 项目添加了图片中文说明功能，在最终报告中为每张图片显示中文说明文字，提升中文用户的阅读体验。

## 功能特性

### 1. 图片中文说明显示
- 在最终报告中，每张图片下方会显示中文说明
- 格式：`![图片描述](图片URL)` 后跟 `(图片说明：中文描述)`
- 保持原有的图片悬停显示功能不变

### 2. 智能翻译
- 自动检测英文图片描述并翻译为中文
- 已包含中文的描述不会重复翻译
- 翻译失败时优雅降级，使用原始描述

### 3. 双重描述存储
- `description`: 用于悬停显示（翻译后的中文）
- `chineseDescription`: 用于最终报告中的图片说明

## 技术实现

### 1. 类型定义扩展 (`src/types.d.ts`)

```typescript
interface ImageSource {
  url: string;
  description?: string;
  chineseDescription?: string; // 新增：中文描述字段
}
```

### 2. 最终报告图片引用规则 (`src/constants/prompts.ts`)

更新了 `finalReportCitationImagePrompt`，指导AI在最终报告中正确引用图片：

```typescript
export const finalReportCitationImagePrompt = `Image Rules:

- Images related to the paragraph content at the appropriate location in the article according to the image description.
- Include images using \`![Image Description](image_url)\` format.
- **IMPORTANT**: After each image, add a new line with the Chinese description in parentheses format: \`(图片说明：中文描述)\`
- Example format:
  \`\`\`
  ![English Description](image_url)
  (图片说明：中文描述)
  \`\`\`
- **Do not add any images at the end of the article.**`;
```

### 3. 图片列表生成逻辑 (`src/utils/deep-research/prompts.ts`)

修改了 `writeFinalReportPrompt` 函数中的图片列表生成：

```typescript
const imageList = images.map(
  (source, idx) => `${idx + 1}. ![${source.description}](${source.url})\n   (图片说明：${source.chineseDescription || source.description})`
);
```

### 4. 图片描述翻译功能 (`src/utils/translate.ts`)

新增专门的图片描述翻译模块：

```typescript
export async function translateImageDescriptions(
  images: ImageSource[],
  aiProvider: any
): Promise<ImageSource[]>
```

**主要功能**：
- 批量翻译图片描述
- 智能检测中文内容，避免重复翻译
- 同时保存原始描述和中文描述
- 错误处理和优雅降级

### 5. 搜索结果处理 (`src/utils/deep-research/index.ts`)

在搜索结果处理流程中集成图片翻译：

```typescript
// 翻译图片描述为中文
if (images.length > 0) {
  images = await translateImageDescriptions(images, this.getTaskModel());
}
```

## 使用流程

### 1. 搜索阶段
1. 执行搜索查询，获取包含图片的搜索结果
2. 自动检测图片描述语言
3. 将英文描述翻译为中文
4. 同时保存原始描述和中文描述

### 2. 最终报告生成
1. AI根据图片引用规则在报告中插入图片
2. 每张图片下方自动添加中文说明
3. 格式：`(图片说明：中文描述)`

### 3. 显示效果
```markdown
![AI Generated Image](https://example.com/image.jpg)
(图片说明：人工智能生成的图像示例)
```

## 配置选项

### 翻译提示词 (`src/constants/prompts.ts`)

```typescript
export const imageDescriptionTranslatePrompt = `请将以下英文图片描述翻译为简洁的中文，保持原意不变。

图片描述列表：
{descriptions}

要求：
1. 翻译要准确、简洁
2. 保持专业术语的准确性
3. 适合作为图片说明使用
4. 返回JSON格式：{"translations": ["翻译1", "翻译2", ...]}

只返回JSON格式的翻译结果，不要包含其他内容。`;
```

## 兼容性说明

### 向后兼容
- 现有的图片悬停显示功能保持不变
- 不影响现有的搜索和报告生成流程
- 翻译失败时优雅降级，不影响整体功能

### 类型安全
- 新增的 `chineseDescription` 字段为可选字段
- 不会破坏现有的类型定义和接口

## 错误处理

### 翻译失败处理
1. JSON解析失败：使用原始描述
2. AI调用失败：使用原始描述
3. 网络错误：使用原始描述
4. 记录警告日志，不影响主流程

### 日志记录
```typescript
console.log(`成功翻译 ${translations.length} 个图片描述`);
console.warn("图片描述翻译失败，使用原始描述:", error);
```

## 性能优化

### 批量翻译
- 一次API调用处理多个图片描述
- 减少网络请求次数
- 提高翻译效率

### 智能过滤
- 只翻译英文描述，跳过已有中文的内容
- 避免不必要的翻译请求

## 测试建议

### 功能测试
1. 创建包含图片的研究任务
2. 验证最终报告中图片说明显示
3. 检查图片悬停功能是否正常
4. 测试中英文双语搜索场景

### 边界测试
1. 无图片的搜索结果
2. 图片描述为空的情况
3. 翻译API失败的情况
4. 混合中英文描述的处理

## 未来扩展

### 可能的改进方向
1. 支持更多语言的图片描述翻译
2. 图片说明的自定义格式选项
3. 图片说明的手动编辑功能
4. 图片说明的缓存机制

---

**实现日期**: 2025-07-10  
**版本**: v1.0  
**状态**: 已完成并测试通过
