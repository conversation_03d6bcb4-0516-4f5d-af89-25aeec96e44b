# 添加新报告格式的详细步骤指南

## 概述

本文档提供添加新报告格式的完整步骤，以添加"市场研究报告"格式为例进行说明。

## 前置条件

- 确保开发环境已搭建
- 了解TypeScript基础语法
- 熟悉项目的基本结构

## 步骤一：更新类型定义

### 文件位置：`src/types/report.ts`

#### 1.1 更新ReportType类型
找到第5行，修改ReportType类型定义：

```typescript
// 修改前
export type ReportType = 'standard' | 'collateral-analysis' | 'custom';

// 修改后
export type ReportType = 'standard' | 'collateral-analysis' | 'custom' | 'market-research';
```

#### 1.2 添加格式配置
在第26行的REPORT_CONFIGS对象中添加新格式配置：

```typescript
export const REPORT_CONFIGS: Record<ReportType, ReportConfig> = {
  standard: {
    type: 'standard',
    planWeight: 'strong',
    name: 'Standard Report',
    description: 'Comprehensive research report following the original plan structure',
    icon: '📄'
  },
  'collateral-analysis': {
    type: 'collateral-analysis',
    planWeight: 'weak',
    name: 'Collateral Analysis',
    description: 'Financial collateral analysis report format for institutional research',
    icon: '📊'
  },
  custom: {
    type: 'custom',
    planWeight: 'weak',
    name: 'Custom Format',
    description: 'User-defined custom report format with personalized prompt',
    icon: '✏️'
  },
  // 新增：市场研究报告格式
  'market-research': {
    type: 'market-research',
    planWeight: 'weak',
    name: 'Market Research Report',
    description: 'Comprehensive market analysis with competitive intelligence and strategic recommendations',
    icon: '📈'
  }
};
```

## 步骤二：定义格式Prompt

### 文件位置：`src/utils/deep-research/report-formats.ts`

#### 2.1 添加格式指导
在第83行的formatInstructions对象中添加新格式的prompt：

```typescript
const formatInstructions = {
  standard: `Write a comprehensive research report based on the report plan by thoroughly analyzing and integrating all the learnings from research.
Pay attention to consolidating similar information, avoiding repetition, and ensuring coherence and logical flow of the report.
Make it as detailed as possible, aim for 5 pages or more, the more the better.`,

  'collateral-analysis': `## **Core Structure**
**Title Format**: "Research/Analysis on [Event/Policy/Topic]" or "Impact Analysis of [Policy/Event] from [Perspective]"
// ... 现有内容 ...`,

  custom: `{customPrompt}`,

  // 新增：市场研究报告格式
  'market-research': `Write a comprehensive market research report with the following structure:

## **Executive Summary**
- Market size and growth projections (include specific numbers and percentages)
- Key findings and strategic implications
- Investment recommendations with risk assessment

## **Market Overview**
- Industry definition and scope
- Market segmentation analysis (by geography, demographics, product type)
- Current market size and historical growth trends
- Key growth drivers and market constraints

## **Competitive Landscape**
- Market share analysis of top 5-10 players
- Competitive positioning matrix
- SWOT analysis of key competitors
- Competitive advantages and differentiation strategies

## **Consumer Analysis**
- Target audience demographics and psychographics
- Buying behavior patterns and decision-making process
- Customer journey mapping
- Unmet needs and pain points analysis

## **Market Trends & Opportunities**
- Emerging trends and disruptive technologies
- Regulatory changes and their impact
- Future market evolution and growth opportunities
- New market segments and untapped potential

## **Strategic Recommendations**
- Market entry strategies for different segments
- Product positioning and pricing recommendations
- Distribution channel optimization
- Risk mitigation strategies and contingency plans

## **Financial Projections**
- Revenue forecasts for next 3-5 years
- Market share projections
- Investment requirements and ROI analysis

**Writing Requirements:**
- Use data-driven insights throughout the report
- Include specific metrics, KPIs, and quantitative analysis
- Provide actionable and implementable recommendations
- Maintain professional consulting tone
- Support all claims with evidence from research data
- Include relevant charts and graphs references where applicable`
};
```

## 步骤三：添加国际化支持

### 文件位置：`src/locales/zh-CN.json`

#### 3.1 添加中文翻译
在第59行的finalReport部分添加新格式的中文翻译：

```json
{
  "research": {
    "finalReport": {
      "title": "4. 最终报告",
      "emptyTip": "等待数据汇总...",
      "researchedInfor": "已研究 {{total}} 个网站",
      "localResearchedInfor": "已研究 {{total}} 个本地资源",
      "writingRequirementLabel": "写作要求（可选）",
      "writingRequirementPlaceholder": "您可以提出任何与撰写报告相关的要求。",
      "reportType": "报告格式",
      "selectReportType": "选择报告格式",
      "reportTypes": {
        "standard": "标准综合报告",
        "collateral-analysis": "担保品分析报告",
        "custom": "自定义格式",
        "market-research": "市场研究报告"
      },
      "reportTypeDescriptions": {
        "standard": "基于研究计划的详细综合报告，适合全面了解主题",
        "collateral-analysis": "专业的金融担保品分析报告格式，适合机构研究",
        "custom": "使用自定义prompt的个性化报告格式",
        "market-research": "全面的市场分析报告，包含竞争情报和战略建议"
      }
    }
  }
}
```

### 文件位置：`src/locales/en-US.json`

#### 3.2 添加英文翻译
在第55行的finalReport部分添加新格式的英文翻译：

```json
{
  "research": {
    "finalReport": {
      "title": "4. Final Report",
      "emptyTip": "Waiting for data to be collated...",
      "researchedInfor": "Researched {{total}} websites",
      "localResearchedInfor": "Researched {{total}} local resources",
      "writingRequirementLabel": "Writing requirements (optional)",
      "writingRequirementPlaceholder": "You can raise any request related to report writing.",
      "reportType": "Report Format",
      "selectReportType": "Select report format",
      "reportTypes": {
        "standard": "Standard Comprehensive Report",
        "collateral-analysis": "Collateral Analysis Report",
        "custom": "Custom Format",
        "market-research": "Market Research Report"
      },
      "reportTypeDescriptions": {
        "standard": "Detailed comprehensive report following the research plan structure",
        "collateral-analysis": "Professional financial collateral analysis report format for institutional research",
        "custom": "Personalized report format using custom prompts",
        "market-research": "Comprehensive market analysis with competitive intelligence and strategic recommendations"
      }
    }
  }
}
```

## 步骤四：更新表单验证

### 文件位置：`src/components/Research/FinalReport/index.tsx`

#### 4.1 更新表单Schema
找到第58行的formSchema定义，更新enum值：

```typescript
// 修改前
const formSchema = z.object({
  requirement: z.string().optional(),
  reportType: z.enum(['standard', 'collateral-analysis', 'custom']),
  customPrompt: z.string().optional(),
  selectedCustomFormatId: z.string().nullable().optional(),
});

// 修改后
const formSchema = z.object({
  requirement: z.string().optional(),
  reportType: z.enum(['standard', 'collateral-analysis', 'custom', 'market-research']),
  customPrompt: z.string().optional(),
  selectedCustomFormatId: z.string().nullable().optional(),
});
```

## 步骤五：测试验证

### 5.1 构建测试
```bash
npm run build
```

### 5.2 启动开发服务器
```bash
npm run dev
```

### 5.3 功能测试清单
- [ ] 新格式在下拉菜单中正确显示
- [ ] 格式名称和描述正确显示（中英文）
- [ ] 选择新格式后能正常生成报告
- [ ] 生成的报告符合预期的格式结构
- [ ] 引用链接功能正常工作
- [ ] 图片引用功能正常工作

## 步骤六：提交代码

### 6.1 添加文件到Git
```bash
git add .
```

### 6.2 提交更改
```bash
git commit -m "feat: add market research report format

- Add market-research report type with comprehensive structure
- Include competitive analysis and strategic recommendations sections
- Add bilingual internationalization support
- Update form validation schema
- Maintain compatibility with existing citation system"
```

## 常见问题排查

### Q1: 构建失败，提示类型错误
**解决方案**: 检查ReportType类型定义是否正确更新，确保所有引用该类型的地方都包含新格式。

### Q2: 新格式在UI中不显示
**解决方案**: 检查国际化文件是否正确添加翻译文本，确保key值与格式ID一致。

### Q3: 生成报告时出错
**解决方案**: 检查formatInstructions中的prompt语法是否正确，确保没有语法错误。

### Q4: 引用功能不工作
**解决方案**: 确保新格式的planWeight设置正确，检查buildFinalReportPrompt函数调用是否传递了正确的参数。

## 高级配置

### 自定义Plan权重
如果需要为新格式设置特殊的Plan权重行为，可以在formatInstructions中添加特殊处理逻辑。

### 添加格式特定的验证
可以在validateCustomPrompt函数中添加针对特定格式的验证规则。

### 格式预览功能
未来可以考虑添加格式预览功能，让用户在选择格式时能够预览报告结构。

## 总结

添加新报告格式需要修改4个主要文件：
1. `src/types/report.ts` - 类型定义和配置
2. `src/utils/deep-research/report-formats.ts` - Prompt定义
3. `src/locales/zh-CN.json` 和 `src/locales/en-US.json` - 国际化
4. `src/components/Research/FinalReport/index.tsx` - 表单验证

按照本文档的步骤操作，可以确保新格式的正确添加和功能完整性。
