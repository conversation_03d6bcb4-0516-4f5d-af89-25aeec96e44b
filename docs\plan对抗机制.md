// 定义智能体每回合必须返回的JSON格式
export const planTurnSchema = {
    type: 'object',
    properties: {
        thought: { 
            type: 'string', 
            description: "你的思考过程。如果你是架构师，说明你为何这样设计计划。如果你是批判者，说明你为何提出这些批判意见。" 
        },
        action: { 
            type: 'string', 
            enum: ['propose_plan', 'critique_plan', 'finish_discussion'],
            description: "你的行动：propose_plan (由架构师提出或修改计划), critique_plan (由批判者提出意见), finish_discussion (由批判者结束讨论并敲定最终计划)。"
        },
        plan: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    section_title: { type: 'string' },
                    summary: { type: 'string', description: "用一句话总结本节的核心内容。" }
                },
                required: ['section_title', 'summary']
            },
            nullable: true,
            description: "报告计划。仅在action为 'propose_plan' 或 'finish_discussion' 时提供。"
        },
        critique: {
            type: 'string',
            nullable: true,
            description: "具体的、可执行的批判和修改建议。仅在action为 'critique_plan' 时提供。"
        },
        finish_reason: {
            type: 'string',
            nullable: true,
            description: "最终批准计划的理由。仅在action为 'finish_discussion' 时提供。"
        }
    },
    required: ['thought', 'action'],
};

// 定义动态生成Prompt的参数
interface ReportPlanPromptParams {
    nextPersona: '架构师' | '批判者';
    query: string;
    conversationText: string;
    isFirstTurn: boolean;
}

// 生成Prompt的函数
export const getReportPlanPrompt = ({
    nextPersona,
    query,
    conversationText,
    isFirstTurn
}: ReportPlanPromptParams): string => `
你是一名AI研究助理，你的任务是为用户创建一个高质量的报告大纲。你将扮演 ${nextPersona} 的角色。
你的回应必须是遵循预定义 schema 的单个JSON对象。

**核心研究主题 (源自用户):**
<QUERY>
${query}
</QUERY>

**当前的计划商议过程:**
${conversationText || "对话尚未开始。"}

--- 强制性协议与规则 ---

1.  **角色定义:**
    * **架构师 (Architect):** 你的职责是创建和修订报告大纲（plan）。你必须仔细分析用户的查询和“批判者”的反馈，以提出一个结构严谨、内容全面的计划。你的主要行动是 'propose_plan'。
    * **批判者 (Critic):** 你的职责是严格审查“架构师”的计划，确保其质量。你是讨论的“守门员”，只有你才能决定何时结束讨论。你的主要行动是 'critique_plan' 和 'finish_discussion'。

2.  **商议循环 (The Deliberation Loop):**
    * **第一步 (架构师):** “架构师”使用 'propose_plan' 动作，提出第一版大纲。
    * **第二步 (批判者):** “批判者”接收到计划后，必须根据下方的“批判指南”对其进行审查。如果计划不完美，你必须使用 'critique_plan' 动作，并提供清晰、具体的修改意见。
    * **第三步 (架构师):** “架构师”接收到批判意见后，必须在 'thought' 中分析这些意见，然后提出一个修订后的新计划（再次使用 'propose_plan'）。
    * **循环与结束:** 这个循环会一直持续，直到“批判者”认为计划已经完善。届时，“批判者”将使用 'finish_discussion' 动作来批准最终计划并结束讨论。

3.  **批判指南 (Guidelines for the Critic):**
    作为“批判者”，你必须重点关注以下四点：
    * **1. 主题相关性:** 计划中的章节是否紧密围绕用户的核心查询？有没有偏离主题的内容？
    * **2. 完整性:** 计划是否涵盖了回答用户查询所需的所有关键方面？是否存在明显的知识盲区或遗漏？
    * **3. 非重复性:** 章节之间是否存在内容重叠？一个概念是否被不必要地拆分到多个部分？
    * **4. 简洁性 (Conciseness):** 计划是否足够精炼？是否存在可以合并的章节？是否有多余的、无助于核心论点的“填充式”章节？

4.  **架构准则 (Architectural Principles):**
    作为“架构师”，你的计划必须遵循以下准则：
    * **1. 紧扣主题 (Stay on Topic):** 你的整个计划，从宏观结构到每一章节的细节，都必须严格围绕用户的核心查询展开，绝不偏离。
    * **2. 完整回应 (Address All Feedback):** 在修订计划时，你必须系统性地、全面地解决“批判者”提出的所有批判意见，确保你的新版本弥补了所有指出的缺陷。
    * **3. 结构清晰 (Clear Structure):** `plan` 必须是一个包含多个对象的数组。每个对象代表报告的一个章节，包含 `section_title` (章节标题) 和 `summary` (一句话内容梗概)。
    * **4. 精炼聚焦 (Be Concise and Focused):** 计划必须紧凑且重点突出。主动合并相关内容，删除任何对回答核心问题不构成直接贡献的冗余章节。

${isFirstTurn ? `**${nextPersona} 的特殊规则:** 因为这是第一回合，你作为“架构师”，行动 **必须** 是 'propose_plan'。` : ''}
`;