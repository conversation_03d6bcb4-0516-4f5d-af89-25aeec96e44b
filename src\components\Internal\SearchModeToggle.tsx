import { useTranslation } from "react-i18next";
import { Languages } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSettingStore } from "@/store/setting";

/**
 * 搜索模式切换组件
 * 允许用户在双语、仅中文、仅英文三种搜索模式之间切换
 */
function SearchModeToggle() {
  const { t } = useTranslation();
  const { searchMode, update } = useSettingStore();

  const handleSearchModeChange = (mode: SearchMode) => {
    update({ searchMode: mode });
  };

  const getCurrentModeLabel = () => {
    switch (searchMode) {
      case "bilingual":
        return "双语";
      case "chinese":
        return "中文";
      case "english":
        return "English";
      default:
        return "双语";
    }
  };

  const getCurrentModeDescription = () => {
    switch (searchMode) {
      case "bilingual":
        return t("research.searchMode.bilingualDescription", "同时使用中英文搜索");
      case "chinese":
        return t("research.searchMode.chineseDescription", "仅使用中文搜索");
      case "english":
        return t("research.searchMode.englishDescription", "仅使用英文搜索");
      default:
        return t("research.searchMode.bilingualDescription", "同时使用中英文搜索");
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          title={getCurrentModeDescription()}
        >
          <Languages className="h-4 w-4" />
          <span className="text-sm">{getCurrentModeLabel()}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => handleSearchModeChange("bilingual")}
          className={searchMode === "bilingual" ? "bg-accent" : ""}
        >
          <div className="flex flex-col">
            <span className="font-medium">双语</span>
            <span className="text-xs text-muted-foreground">
              {t("research.searchMode.bilingualDescription", "同时使用中英文搜索")}
            </span>
          </div>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleSearchModeChange("chinese")}
          className={searchMode === "chinese" ? "bg-accent" : ""}
        >
          <div className="flex flex-col">
            <span className="font-medium">中文</span>
            <span className="text-xs text-muted-foreground">
              {t("research.searchMode.chineseDescription", "仅使用中文搜索")}
            </span>
          </div>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleSearchModeChange("english")}
          className={searchMode === "english" ? "bg-accent" : ""}
        >
          <div className="flex flex-col">
            <span className="font-medium">English</span>
            <span className="text-xs text-muted-foreground">
              {t("research.searchMode.englishDescription", "仅使用英文搜索")}
            </span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default SearchModeToggle;
