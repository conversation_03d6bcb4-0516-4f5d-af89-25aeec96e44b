# 章节式报告改进 - 需求确认文档

## 原始需求
章节式改进，章节报告得到后，目前是全部塞给最终prompt让llm得到最终报告，但是这样对上下文不行，而且实际上都是覆盖重写。所以要改动的是，得到章节报告后，首先通过代码硬性拼接，之后丢给最终报告prompt（在原本的基础上改进）让它只生成一个总结性的章节。最后，再把这个总结性章节拼接回之前拼接的所有章节报告的报告，输出完事儿

## 需求澄清过程

### 第一轮澄清问题
1. **拼接逻辑细节**：章节报告的拼接顺序、分隔符、标题格式
2. **总结性章节规格**：位置、内容、长度限制
3. **技术实现范围**：修改范围、prompt模板、引用编号
4. **用户体验考虑**：预览功能、编辑功能、进度提示

### 用户回答
1. 章节ID顺序，不需要分隔符，章节标题必须保留
2. 总结放在末尾，和其他章节格式一样，包含结论，无长度限制
3. 修改generateFinalReport函数，修改现有prompt，暂不改引用编号
4. 不需要预览，保持原有流程，不改编辑功能，不需要进度提示

## 最终确认需求

### 功能目标
优化章节式研究的最终报告生成流程，解决上下文过长和内容覆盖重写问题

### 核心改进点
1. **代码硬性拼接**：将所有章节报告按章节ID顺序拼接，保留章节标题
2. **总结性章节生成**：修改最终报告prompt，让LLM只生成一个总结性章节
3. **最终报告组装**：将总结性章节拼接到所有章节报告末尾

### 技术实现要求
- 修改 `src/hooks/useChapterResearch.ts` 中的 `generateFinalReport` 函数
- 修改现有的最终报告生成prompt
- 保持现有的引用编号系统不变
- 保持现有的用户体验流程不变

### 质量标准
- 解决上下文长度问题，提升生成效率
- 避免章节内容被覆盖重写
- 保持报告结构的完整性和逻辑性
- 确保总结性章节与其他章节格式一致

## 需求质量评分：95/100

**功能清晰度 (28/30分)：**
- ✅ 明确了拼接逻辑：章节ID顺序，保留标题，无分隔符
- ✅ 明确了总结性章节：末尾位置，结论内容，无长度限制
- ✅ 明确了最终输出格式

**技术特异性 (24/25分)：**
- ✅ 明确修改generateFinalReport函数
- ✅ 明确修改现有prompt而非创建新的
- ✅ 明确保持引用编号系统不变

**实现完整性 (23/25分)：**
- ✅ 明确了三个核心步骤的实现逻辑
- ✅ 明确了保持现有用户体验
- ✅ 明确了不需要额外的预览和进度功能

**业务上下文 (20/20分)：**
- ✅ 解决上下文长度问题，提升性能
- ✅ 避免内容覆盖重写，保持原有章节完整性
- ✅ 保持用户体验一致性

## 确认时间
2025-08-13

## 状态
需求确认完成，等待用户批准进入实现阶段