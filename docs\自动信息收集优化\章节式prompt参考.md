**
 * 1. 开场AI - 章节开始时的第一次发言
 */
export function generateOpeningPrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  allCollectedInfo: string
): string {
  return `# **指令：章节研究开场协议**

当前章节目标：${chapterGoal}

已收集的信息：
${allCollectedInfo || "暂无信息"}

你需要分析上述目标，找出信息缺口。请按以下步骤思考：

1. **分解目标**：把"${chapterGoal}"分成3-5个具体方面
   - 想想完成这个目标需要了解哪些方面
   - 每个方面都应该是具体的、可查找的

2. **检查现有信息**：看看已有信息覆盖了哪些方面
   - 哪些方面已经有信息了？
   - 哪些方面完全没有信息？

3. **找出缺口**：选择2-3个最缺少信息的方面
   - 优先选择对目标最重要的
   - 确保是能在网上找到的信息

输出格式（必须是JSON）：
{
  "thought": "1. **目标解构**: 我将目标分解为[列出方面]。\\n2. **知识盘点**: 现有信息覆盖了[说明情况]。\\n3. **差距识别**: [说明哪些最缺乏]。",
  "action": {
    "actionName": "HIGH_LEVEL_PROPOSE",
    "payload": {
      "proposal": "提议的薄弱知识点列表：\\n1. **[方面名称]**: (理由: [为什么缺乏])。\\n2. **[方面名称]**: (理由: [为什么缺乏])。"
    }
  }
}`;
}

/**
 * 2. 高层级响应AI - 响应对方的HIGH_LEVEL_PROPOSE
 */
export function generateHighLevelResponsePrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  currentRoundDiscussions: string, // 其中包含了伙伴的提议
  allCollectedInfo: string
): string {

  return `# **指令：高层级提议评审**

当前章节目标：${chapterGoal}

伙伴的提议：
${currentRoundDiscussions}

已收集的信息：
${allCollectedInfo || "暂无信息"}

你需要评审伙伴的提议。按以下步骤思考：

1. **先独立分析**：不看伙伴提议，你自己认为哪些信息最缺乏？
   - 基于章节目标分析
   - 列出你认为最重要的2-3个缺口

2. **再对比决策**：比较你的分析和伙伴的提议
   - 如果基本一致（比如都提到了同样的重点），就同意
   - 如果有重大差异（比如你认为伙伴忽略了关键点），就提出你的方案

输出格式（必须是JSON）：

如果同意：
{
  "thought": "1. **独立分析**: 我认为最缺乏的是[列出你的想法]。\\n2. **对比决策**: 伙伴提议的是[总结伙伴观点]。我们的想法[说明是否一致]，所以我同意。",
  "action": {
    "actionName": "HIGH_LEVEL_AGREE",
    "payload": {
      "agreement": "同意伙伴的提议：[简要复述核心要点]"
    }
  }
}

如果不同意：
{
  "thought": "1. **独立分析**: 我认为最缺乏的是[列出你的想法]。\\n2. **对比决策**: 伙伴提议的是[总结伙伴观点]。但我认为[说明差异]，所以提出修正。",
  "action": {
    "actionName": "HIGH_LEVEL_PROPOSE",
    "payload": {
      "proposal": "我提议的薄弱知识点列表：\\n1. **[方面名称]**: (理由: [为什么重要])。\\n2. **[方面名称]**: (理由: [为什么重要])。"
    }
  }
}`;
}


/**
 * 3. 查询开始AI - 高层级达成共识后开始查询阶段
 */
export function generateQueryStartPrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  highLevelConsensus: string,
  allCollectedInfo: string
): string {

  return `# **指令：制定查询计划**

共识的研究方向：${highLevelConsensus}

章节目标：${chapterGoal}

已有信息：
${allCollectedInfo || "暂无信息"}

你需要制定具体的搜索查询。按以下步骤思考：

1. **找出真正需要查的**：
   - 看看共识中提到的每个方面
   - 检查已有信息是否已经回答了
   - 列出还不知道的具体问题

2. **设计搜索词**：
   - 每个问题用1-3个不同角度的搜索词
   - 搜索词要具体、容易找到结果
   - 可以从这些角度搜索：
     * 直接查找：关键词 + 具体细节
     * 深入了解：关键词 + 原理/机制/分析
     * 多方视角：关键词 + 评价/比较/案例

输出格式（必须是JSON）：
{
  "thought": "1. **知识盘点**: 共识要研究[总结要点]。检查已有信息，发现[说明哪些已知，哪些未知]。\\n2. **查询设计**: 针对[未知内容]，我设计了以下查询。",
  "action": {
    "actionName": "QUERY_PROPOSE",
    "payload": {
      "queries": [
        {
          "query": "[具体的搜索词]",
          "researchGoal": "[这个查询要解决什么问题]"
        },
        {
          "query": "[具体的搜索词]",
          "researchGoal": "[这个查询要解决什么问题]"
        }
      ]
    }
  }
}

注意：
- 每个query都要对应一个具体的知识缺口
- researchGoal要简短清晰
- 避免过于宽泛的搜索词`;
}

/**
 * 4. 查询响应AI - 响应对方的QUERY_PROPOSE
 */
export function generateQueryResponsePrompt(
  agentId: 'Alpha' | 'Beta',
  chapterGoal: string,
  highLevelConsensus: string,
  currentQueryDiscussions: string, // 其中包含了伙伴的QUERY_PROPOSE
  allCollectedInfo: string
): string {
  
  return `# **指令：战术查询审核协议**

共识的研究方向：${highLevelConsensus}

章节目标：${chapterGoal}

伙伴的查询提议：
${currentQueryDiscussions}

已有信息：
${allCollectedInfo || "暂无信息"}

你需要审核伙伴的查询提议。按以下步骤思考：

1. **先想想你会查什么**：
   - 不看伙伴的提议
   - 基于共识方向，你觉得最该查什么？
   - 列出你认为最重要的2-3个查询

2. **再看伙伴的提议**：
   - 伙伴的查询是否符合我们的共识方向？
   - 查询词是否具体、容易找到结果？
   - 是否遗漏了重要问题？

3. **做出决定**：
   - 如果伙伴的查询很好，就执行
   - 如果有问题，就提出你的查询方案

输出格式（必须是JSON）：

如果同意执行：
{
  "thought": "1. **我的想法**: 我认为应该查[你的想法]。\\n2. **评估伙伴提议**: 伙伴提议查[总结伙伴提议]。这些查询[说明为什么好]。\\n3. **决定**: 伙伴的提议很全面，可以执行。",
  "action": {
    "actionName": "EXECUTE_QUERIES",
    "payload": {
      "queries": [伙伴提议的查询列表]
    }
  }
}

如果要修改：
{
  "thought": "1. **我的想法**: 我认为应该查[你的想法]。\\n2. **评估伙伴提议**: 伙伴提议查[总结伙伴提议]。但[说明问题]。\\n3. **决定**: 需要调整查询方案。",
  "action": {
    "actionName": "QUERY_PROPOSE",
    "payload": {
      "queries": [
        {
          "query": "[你的搜索词]",
          "researchGoal": "[要解决什么问题]"
        }
      ],
      "rationale": "[简单说明为什么你的方案更好]"
    }
  }
}`;
}

