#!/bin/bash

# Deep Research 服务器启动脚本
# 使用方法: ./start-server.sh [端口号]

# 设置默认端口
DEFAULT_PORT=3001
PORT=${1:-$DEFAULT_PORT}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "❌ 端口 $1 已被占用"
        echo "正在使用端口 $1 的进程："
        lsof -Pi :$1 -sTCP:LISTEN
        echo ""
        echo "请选择以下操作："
        echo "1. 杀死占用端口的进程: sudo kill -9 \$(lsof -t -i:$1)"
        echo "2. 使用其他端口: ./start-server.sh [新端口号]"
        return 1
    else
        echo "✅ 端口 $1 可用"
        return 0
    fi
}

# 停止现有的Deep Research进程
stop_existing() {
    echo "🔍 检查现有的Deep Research进程..."
    EXISTING_PID=$(pgrep -f "next start")
    if [ ! -z "$EXISTING_PID" ]; then
        echo "🛑 发现现有进程 PID: $EXISTING_PID，正在停止..."
        kill -9 $EXISTING_PID
        sleep 2
    fi
}

# 启动服务器
start_server() {
    echo "🚀 启动Deep Research服务器..."
    echo "📍 端口: $PORT"
    echo "📁 工作目录: $(pwd)"
    echo "📝 日志文件: app.log"
    echo ""
    
    # 设置环境变量并启动
    export PORT=$PORT
    nohup pnpm start > app.log 2>&1 &
    
    # 获取进程ID
    SERVER_PID=$!
    echo "✅ 服务器已启动"
    echo "🆔 进程ID: $SERVER_PID"
    echo "🌐 访问地址: http://localhost:$PORT"
    echo "📋 查看日志: tail -f app.log"
    echo "🛑 停止服务: kill -9 $SERVER_PID"
    echo ""
    
    # 等待几秒钟检查启动状态
    sleep 3
    if ps -p $SERVER_PID > /dev/null; then
        echo "🎉 服务器启动成功！"
    else
        echo "❌ 服务器启动失败，请检查日志："
        tail -20 app.log
    fi
}

# 主流程
echo "🔧 Deep Research 服务器启动工具"
echo "================================"

# 停止现有进程
stop_existing

# 检查端口
if check_port $PORT; then
    start_server
else
    echo "❌ 启动失败"
    exit 1
fi
