"use client";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { LoaderCircle, Brain, MessageSquare, CheckCircle, Target, ChevronDown, ChevronRight } from "lucide-react";
import { Button } from "@/components/Internal/Button";
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import useDeepResearch from "@/hooks/useDeepResearch";
import { useTaskStore } from "@/store/task";

interface AdversarialPlanOptimizerProps {
  onClose: () => void;
}

export default function AdversarialPlanOptimizer({ onClose }: AdversarialPlanOptimizerProps) {
  const { t } = useTranslation();
  const { status, runAdversarialPlanOptimization } = useDeepResearch();
  const taskStore = useTaskStore();
  const [maxRounds, setMaxRounds] = useState(5);
  const [mode, setMode] = useState<'fixed' | 'auto'>('auto');
  const [isExpanded, setIsExpanded] = useState(true);

  const adversarialPlan = taskStore.adversarialPlan;
  const isRunning = adversarialPlan?.isRunning || false;
  const rounds = adversarialPlan?.rounds || [];
  const currentRound = adversarialPlan?.currentRound || 0;
  const currentPersona = adversarialPlan?.currentPersona || '架构师';
  const conversationText = adversarialPlan?.conversationText || '';
  const [showRawLog, setShowRawLog] = useState(false);

  const handleStart = async () => {
    taskStore.initAdversarialPlan(maxRounds, mode);
    await runAdversarialPlanOptimization(maxRounds, mode);
  };

  const renderCollapsedState = () => {
    if (!adversarialPlan?.isActive) {
      return <Badge variant="secondary">{t('research.adversarial.status.pending', 'Pending Configuration')}</Badge>;
    }
    if (isRunning) {
      return <Badge variant="default">{t('research.adversarial.status.running', { currentRound: currentRound })}</Badge>;
    }
    if (taskStore.reportPlan) {
      return <Badge variant="default" className="bg-green-100 text-green-800">✅ {t('research.adversarial.status.completed', 'Optimization Completed')}</Badge>;
    }
    return null;
  };

  const getProgress = () => {
    if (mode === 'auto') {
      // 自动模式下显示当前进度，最多显示到最大轮数
      return Math.min((currentRound / maxRounds) * 100, 100);
    }
    return (currentRound / maxRounds) * 100;
  };

  const getRoundIcon = (persona: '架构师' | '批判者', action: string) => {
    if (persona === '架构师') {
      return <Brain className="h-4 w-4 text-blue-500" />;
    }
    if (action === 'finish_discussion') {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    return <Target className="h-4 w-4 text-orange-500" />;
  };

  const getPersonaColor = (persona: '架构师' | '批判者') => {
    return persona === '架构师' ? 'bg-blue-100 text-blue-800' : 'bg-orange-100 text-orange-800';
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'propose_plan':
        return 'bg-green-100 text-green-800';
      case 'critique_plan':  
        return 'bg-yellow-100 text-yellow-800';
      case 'finish_discussion':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full border-dashed border-2 p-4">
      <CardHeader className="cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            {isExpanded ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
            <MessageSquare className="h-5 w-5" />
            {t('research.adversarial.title', 'Advanced Plan Optimization')}
          </CardTitle>
          {!isExpanded && renderCollapsedState()}
        </div>
        {isExpanded && (
          <CardDescription className="pt-2">
            {t('research.adversarial.description', 'Use adversarial optimization to enhance your research plan through architect-critic dialogue')}
          </CardDescription>
        )}
      </CardHeader>

      {isExpanded && (
        <>
          <div className="space-y-4 p-4">
            {/* 控制面板 */}
            {!adversarialPlan?.isActive && (
              <div className="p-4 border rounded-lg bg-gray-50">
                <h4 className="text-lg font-semibold">
                  {t('research.adversarial.settings', 'Optimization Settings')}
                </h4>
                <p className="text-sm text-gray-500 mb-4">
                  {t('research.adversarial.settingsDesc', 'Configure the adversarial optimization parameters')}
                </p>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {t('research.adversarial.maxRounds', 'Max Rounds')}
                    </label>
                    <select
                      value={maxRounds}
                      onChange={(e) => setMaxRounds(parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isRunning}
                    >
                      <option value={2}>2 rounds</option>
                      <option value={4}>4 rounds</option>
                      <option value={6}>6 rounds</option>
                      <option value={8}>8 rounds</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {t('research.adversarial.mode', 'Mode')}
                    </label>
                    <select
                      value={mode}
                      onChange={(e) => setMode(e.target.value as 'fixed' | 'auto')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isRunning}
                    >
                      <option value="auto">
                        {t('research.adversarial.autoMode', 'Auto (Critic decides when to stop)')}
                      </option>
                      <option value="fixed">
                        {t('research.adversarial.fixedMode', 'Fixed rounds')}
                      </option>
                    </select>
                  </div>
                </div>
                <div className="mt-4 flex gap-2">
                  <Button
                    onClick={handleStart}
                    disabled={isRunning || !taskStore.query}
                    className="w-full"
                  >
                    {isRunning ? (
                      <>
                        <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                        {t('research.adversarial.running', 'Optimizing...')}
                      </>
                    ) : (
                      <>
                        <Brain className="mr-2 h-4 w-4" />
                        {t('research.adversarial.start', 'Start Optimization')}
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={onClose}
                    className="w-full"
                    disabled={isRunning}
                  >
                    {t('common.cancel', 'Cancel')}
                  </Button>
                </div>
              </div>
            )}
            
            {adversarialPlan?.isActive && (
              <>
                {/* 进度显示 */}
                <div>
                  <div className="flex items-center justify-between">
                    <h4 className="text-lg font-semibold">
                      {t('research.adversarial.progress', 'Optimization Progress')}
                    </h4>
                    <Badge variant="outline">
                      Round {currentRound} / {mode === 'auto' ? '∞' : maxRounds}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-500 mb-2">
                    Current: {currentPersona === '架构师' ? 'Architect' : 'Critic'} • {status}
                  </p>
                  <Progress value={getProgress()} className="w-full" />
                </div>

                <Separator className="my-6" />

                {/* 对话历史 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-lg font-semibold">
                      {t('research.adversarial.dialogue', 'Architect-Critic Dialogue')}
                    </h4>
                    <Button variant="link" size="sm" onClick={() => setShowRawLog(!showRawLog)} className="text-xs">
                      {showRawLog ? t('research.adversarial.hideRawLog', 'Hide Raw Log') : t('research.adversarial.showRawLog', '</> Show Raw Log')}
                    </Button>
                  </div>
                  <ScrollArea className="h-[400px] pr-4 border bg-gray-50/50 rounded-md p-4">
                    {showRawLog ? (
                      <pre className="whitespace-pre-wrap text-xs text-gray-600 bg-black p-4 rounded-md text-white font-mono">
                        {conversationText}
                      </pre>
                    ) : (
                      <div className="space-y-4">
                        {rounds.map((round, index) => (
                          <div key={index} className="border rounded-lg p-4 bg-white shadow-sm transition-all hover:shadow-md">
                            <div className="flex items-center gap-2 mb-3 pb-2 border-b">
                              {getRoundIcon(round.persona, round.action)}
                              <Badge variant="outline" className={`font-semibold ${getPersonaColor(round.persona)}`}>
                                {round.persona === '架构师' ? 'Architect' : 'Critic'}
                              </Badge>
                              <Badge variant="secondary" className={getActionColor(round.action)}>
                                {round.action}
                              </Badge>
                              <span className="text-xs text-gray-500 ml-auto">
                                Round {round.roundNumber}
                              </span>
                            </div>
                            
                            <div className="space-y-3 text-sm pl-1">
                              <div className="border-l-2 pl-4 border-gray-200">
                                <p className="font-medium text-gray-500 text-xs uppercase tracking-wider">Thought Process</p>
                                <p className="mt-1 text-gray-800">{round.thought}</p>
                              </div>
                              
                              {round.plan && Array.isArray(round.plan) && round.plan.length > 0 && (
                                <div className="border-l-2 pl-4 border-blue-300 mt-2 pt-2">
                                  <p className="font-medium text-blue-600 text-xs uppercase tracking-wider">Proposed Plan</p>
                                  <ul className="mt-2 space-y-1 list-disc list-inside text-gray-800">
                                    {round.plan.map((item, i) => (
                                      <li key={i}>
                                        <span className="font-semibold">{item?.section_title || 'Unknown Section'}:</span> {item?.summary || 'No summary provided'}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                              
                              {round.critique && (
                                <div className="border-l-2 pl-4 border-orange-400 mt-2 pt-2">
                                  <p className="font-medium text-orange-600 text-xs uppercase tracking-wider">critique_plan</p>
                                  <div className="mt-1">
                                    {typeof round.critique === 'string' ? (
                                      <p className="text-gray-800">{round.critique}</p>
                                    ) : (
                                      <div className="space-y-2">
                                        {Object.entries(round.critique).map(([category, items]) => (
                                          Array.isArray(items) && items.length > 0 && (
                                            <div key={category} className="bg-orange-50 p-2 rounded">
                                              <p className="font-medium text-orange-700 text-sm capitalize">
                                                {category.replace(/_/g, ' ')}:
                                              </p>
                                              <ul className="mt-1 space-y-1">
                                                {items.map((item, idx) => (
                                                  <li key={idx} className="text-sm text-gray-700 pl-2 border-l border-orange-300">
                                                    {item}
                                                  </li>
                                                ))}
                                              </ul>
                                            </div>
                                          )
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                              
                              {round.finish_reason && (
                                <div className="border-l-2 pl-4 border-green-500 mt-2 pt-2">
                                  <p className="font-medium text-green-600 text-xs uppercase tracking-wider">Approval Reason</p>
                                  <p className="mt-1 text-gray-800">{round.finish_reason}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </div>
              </>
            )}
          </div>

          <CardFooter className="flex justify-end gap-2 pt-4 border-t">
            {!isRunning && adversarialPlan?.isActive && taskStore.reportPlan && (
              <Badge variant="outline" className="mr-auto">
                ✅ {t('research.adversarial.completed', 'Plan optimized and applied')}
              </Badge>
            )}
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isRunning}
            >
              {isRunning ? t('common.running', 'Running...') : t('common.close', 'Close')}
            </Button>
          </CardFooter>
        </>
      )}
    </Card>
  );
}