#!/bin/bash

# Deep Research 监控脚本

echo "📊 Deep Research 运行状况监控"
echo "================================"

# 检查进程状态
echo "🔍 进程状态："
if pgrep -f "next start" > /dev/null; then
    echo "✅ 应用正在运行"
    PID=$(pgrep -f "next start")
    echo "   PID: $PID"
    
    # 显示进程详细信息
    echo "   内存使用: $(ps -o pid,ppid,cmd,%mem,%cpu --sort=-%mem -p $PID | tail -n +2)"
else
    echo "❌ 应用未运行"
    exit 1
fi

echo ""

# 检查端口状态
echo "🌐 端口状态："
if lsof -i :3001 > /dev/null 2>&1; then
    echo "✅ 端口 3001 正在监听"
    lsof -i :3001
else
    echo "❌ 端口 3001 未监听"
fi

echo ""

# 检查应用响应
echo "🏥 应用健康检查："
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3001 | grep -q "200"; then
    echo "✅ 应用响应正常 (HTTP 200)"
else
    echo "❌ 应用响应异常"
fi

echo ""

# 显示最近的日志
echo "📝 最近日志 (最后 10 行)："
if [ -f "logs/app.log" ]; then
    tail -n 10 logs/app.log
else
    echo "   日志文件不存在"
fi

echo ""
echo "💡 有用的命令："
echo "   查看实时日志: tail -f logs/app.log"
echo "   重启应用: pkill -f 'next start' && pnpm start"
echo "   查看详细进程: htop -p $PID"
