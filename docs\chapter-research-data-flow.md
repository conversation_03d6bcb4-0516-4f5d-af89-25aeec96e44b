# 章节式研究数据流转与Prompt机制文档

## 概述

章节式研究是一个基于智能体协作的深度研究系统，通过Alpha和Beta两个智能体在每个章节中进行结构化对话，实现信息收集和分析的自动化。系统分为高层级讨论阶段、查询确定阶段、搜索执行阶段和报告生成阶段。

## 系统架构

### 核心组件

```
章节式研究系统
├── 状态管理 (Zustand Store)
├── 智能体协作引擎 (Agent Collaboration Engine)
├── Prompt生成器 (Prompt Generator)
├── 搜索执行器 (Search Executor) 
└── 报告生成器 (Report Generator)
```

### 数据实体关系

```mermaid
graph TD
    A[用户查询] --> B[计划解析]
    B --> C[章节信息 ChapterInfo]
    C --> D[智能体对话 AgentDiscussion]
    D --> E[收集信息 ChapterCollectedInfo]
    E --> F[章节报告 ChapterReport]
    F --> G[最终报告]
    
    D --> H[搜索查询]
    H --> I[搜索结果]
    I --> E
```

## 数据流转详解

### 1. 研究初始化阶段

**输入**: 用户计划文本 (Markdown格式)
**输出**: 结构化章节列表

```typescript
interface ChapterInfo {
  id: string;
  title: string;              // 章节标题
  goal: string;               // 章节目标（从summary解析）
  status: 'pending' | 'discussing' | 'searching' | 'writing' | 'completed';
  currentRound: number;       // 当前对话轮数
  currentPhase: 'high_level' | 'query' | 'searching'; // 当前阶段
  highLevelConsensus?: string; // 高层级共识结果
  finalQueries?: Array<{      // 最终确定的查询
    query: string;
    researchGoal: string;
  }>;
  searchProgress?: SearchProgress; // 搜索进度状态
}
```

**处理流程**:
1. 调用 `parsePlanToChapters()` 解析Markdown计划
2. 提取章节标题和研究目标
3. 初始化每个章节的状态为 `pending`
4. 生成唯一的章节ID

### 2. 智能体协作阶段

每个章节经历以下对话流程：

#### 2.1 高层级讨论阶段 (HIGH_LEVEL)

**目标**: Alpha和Beta智能体就章节研究方向达成共识

**数据流转**:
```
Alpha (HIGH_LEVEL_PROPOSE) 
  ↓ [提议缺失的关键信息点]
Beta (HIGH_LEVEL_AGREE/HIGH_LEVEL_PROPOSE)
  ↓ [同意或提出修正意见]
... [最多4轮对话]
  ↓ [达成共识]
保存高层级共识到 chapter.highLevelConsensus
```

**智能体讨论记录**:
```typescript
interface AgentDiscussion {
  id: string;
  chapterId: string;
  roundNumber: number;        // 轮数（1,2,3...）
  turnNumber: number;         // 回合数（每轮中Alpha和Beta的对话次数）
  agentId: AgentId;           // 'Alpha' | 'Beta'
  actionName: AgentActionName; // 动作类型
  payload: AgentActionPayload; // 动作负载
  thought: string;            // 思考过程
  timestamp: number;
}
```

#### 2.2 查询确定阶段 (QUERY)

**目标**: 基于高层级共识，确定具体的搜索查询

**数据流转**:
```
Alpha (QUERY_PROPOSE)
  ↓ [基于共识提议具体查询列表]
Beta (EXECUTE_QUERIES/QUERY_PROPOSE)
  ↓ [同意执行或提出修正查询]
... [最多4轮对话]
  ↓ [确定最终查询]
保存查询列表到 chapter.finalQueries
```

#### 2.3 搜索执行阶段 (SEARCHING)

**目标**: 执行确定的查询并收集信息

**数据流转**:
```
执行搜索查询
  ↓ [并行搜索所有查询]
收集搜索结果
  ↓ [整理和清洗结果]
可选: AI总结信息
  ↓ [生成结构化摘要]
保存到 ChapterCollectedInfo
```

### 3. 信息收集与总结机制详解

#### 3.1 搜索执行流程

**第一步**: 执行网络搜索
```typescript
const searchResult = await performSearch(queryText);
```

**第二步**: AI智能总结（可选）
系统提供两种处理模式：

**AI总结模式** (`enableInfoSummarization: true`):
```typescript  
const generateSearchResultSummary = useCallback(async (
  query: string,
  researchGoal: string,
  sources: Source[]
): Promise<string> => {
  const model = await createModelProvider(getModel().networkingModel);
  const result = await generateText({
    model,
    system: getSystemPrompt(),
    prompt: processSearchResultPrompt(query, researchGoal, sources, false),
    temperature: 0.6,
  });
  return result.text; // AI总结后的内容
});
```

**快速模式** (`enableInfoSummarization: false`):
- 直接使用原始搜索内容，无AI处理
- 性能更高，token消耗更少

**第三步**: 信息存储和格式化

#### 3.2 信息收集数据结构

```typescript
interface ChapterCollectedInfo {
  chapterId: string;
  roundNumber: number;
  searchResults: Array<{
    query: string;
    researchGoal: string;
    results: string;          // AI总结后的内容 或 原始搜索内容
    sources: Source[];        // 来源信息
  }>;
  summary: string;            // 轮次统计描述（非AI总结）
}
```

**重要说明**: 
- `summary`字段只是简单的统计描述，如"第1轮收集到3个查询的信息（已总结）"
- 真正的AI总结内容存储在 `searchResults[].results` 字段中
- `getChapterCollectedSummary()` 函数**不执行AI总结**，只是格式化已有数据

#### 3.3 信息总结使用的Prompt

信息总结复用深度研究系统的标准Prompt：
- **使用的Prompt**: `processSearchResultPrompt(query, researchGoal, sources, false)`
- **系统指令**: `getSystemPrompt()`（与其他研究功能保持一致）
- **处理方式**: 将多个搜索来源整合为结构化的研究内容
- **温度参数**: 0.6（平衡准确性和创造性）

### 4. 章节报告生成

**输入**: 
- 章节目标
- 所有收集的信息
- 用户原始查询

**输出**: 
- 结构化章节报告
- 引用来源列表

## 四个核心Prompt机制

### 1. 开场Prompt (generateOpeningPrompt)

**用途**: Alpha智能体首次分析章节目标，识别信息缺口

**关键参数**:
- `chapterGoal`: 章节研究目标
- `userQuery`: 用户原始查询
- `allCollectedInfo`: 已收集的信息（可选）

**输出格式**:
```json
{
  "thought": "目标解构和差距分析",
  "action": {
    "actionName": "HIGH_LEVEL_PROPOSE",
    "payload": {
      "proposal": "薄弱知识点列表"
    }
  }
}
```

**核心逻辑**:
1. 将章节目标分解为3-5个具体方面
2. 检查现有信息的覆盖情况
3. 识别2-3个最关键的信息缺口
4. 确保提议的方面是可搜索的

### 2. 高层级响应Prompt (generateHighLevelResponsePrompt)

**用途**: Beta智能体评估Alpha的提议，决定同意或提出修正

**关键参数**:
- `chapterGoal`: 章节目标
- `currentRoundDiscussions`: 当前轮次的对话历史
- `allCollectedInfo`: 已收集的信息

**输出格式**:
- 同意: `HIGH_LEVEL_AGREE`
- 不同意: `HIGH_LEVEL_PROPOSE`

**决策逻辑**:
1. 独立分析章节目标的研究需求
2. 对比自己的分析和伙伴的提议
3. 评估提议的完整性和重要性
4. 决定同意还是提出修正方案

### 3. 查询开始Prompt (generateQueryStartPrompt)

**用途**: 基于高层级共识，制定具体的搜索查询计划

**关键参数**:
- `highLevelConsensus`: 高层级阶段达成的共识
- `chapterGoal`: 章节目标
- `allCollectedInfo`: 已收集的信息

**输出格式**:
```json
{
  "thought": "查询设计思路",
  "action": {
    "actionName": "QUERY_PROPOSE",
    "payload": {
      "queries": [
        {
          "query": "具体搜索词",
          "researchGoal": "解决什么问题"
        }
      ]
    }
  }
}
```

**设计原则**:
1. 针对共识中的每个方面设计查询
2. 每个查询对应一个具体的知识缺口
3. 查询词要具体、容易找到结果
4. 支持多角度搜索（直接查找、深入了解、多方视角）

### 4. 查询响应Prompt (generateQueryResponsePrompt)

**用途**: Beta智能体审核Alpha的查询提议，决定执行还是修正

**关键参数**:
- `highLevelConsensus`: 共识的研究方向
- `currentQueryDiscussions`: 当前查询阶段的讨论
- `chapterGoal`: 章节目标
- `allCollectedInfo`: **历史轮次收集的信息总结**（帮助避免重复查询）

**输出格式**:
- 执行查询: `EXECUTE_QUERIES`
- 修正查询: `QUERY_PROPOSE`

**审核标准**:
1. 查询是否符合共识方向
2. 查询词是否具体、容易找到结果
3. 是否遗漏了重要问题
4. 是否与已收集信息重复
5. 查询的完整性和有效性

## 最终章节式报告Prompt机制

### 章节报告生成

**用途**: 为每个章节生成专门的报告内容

**输入数据**:
- 章节标题和目标
- 该章节收集的所有信息
- 搜索来源和引用
- 用户原始查询作为背景

**生成流程**:
1. 分析章节目标和收集的信息
2. 根据信息质量和完整性组织内容
3. 添加适当的引用和来源链接
4. 确保内容与章节目标高度相关

**输出格式**:
```typescript
interface ChapterReport {
  chapterId: string;
  title: string;
  content: string;           // 章节报告内容（Markdown格式）
  sources: Source[];         // 引用来源
  generatedAt: number;       // 生成时间
}
```

### 整合最终报告

**目标**: 将所有章节报告整合成完整的研究报告

**整合策略**:
1. 按章节顺序组织内容
2. 统一引用编号和格式
3. 去除章节间的重复内容
4. 确保整体逻辑连贯性
5. 添加统一的引用列表

## 配置参数

### 系统配置
```typescript
interface ChapterResearchConfig {
  maxRounds: number;         // 每章节最大研究轮数 (默认3)
  highLevelTurns: number;    // 高层级阶段最大对话回合数 (默认4)
  queryTurns: number;        // 查询阶段最大对话回合数 (默认4)
  autoExecuteQueries: boolean; // 是否自动执行查询 (默认true)
  enableInfoSummarization: boolean; // 是否启用信息总结 (默认false)
}
```

### 性能优化

**上下文缓存**: 
- 对已完成轮次的上下文进行缓存
- 避免重复计算讨论历史
- 提高响应速度

**状态同步优化**:
- 使用 `useTaskStore.getState()` 获取最新状态
- 避免状态同步延迟问题
- 确保智能体能获得完整的对话历史

## 错误处理和异常情况

### AI响应解析
- 支持Markdown代码块包装的JSON响应
- 自动清理 `\`\`\`json` 标记
- JSON格式验证和错误恢复

### 智能体对话异常
- 最大轮次限制防止无限循环
- 解析失败时的重试机制
- 状态不一致时的修复逻辑

### 搜索执行异常
- 搜索失败时的降级处理
- 部分查询成功时的继续机制
- 搜索结果质量验证

## 监控和调试

### 详细日志记录
```
🚀 [章节研究] 启动章节式研究
🤖 [智能体对话] Alpha 执行 HIGH_LEVEL_PROPOSE
📚 [调用前状态] 该章节现有讨论数: X
🔍 [上下文收集] 收集讨论上下文
💾 [保存前] 准备保存讨论记录
✅ [Zustand] 成功添加讨论
🔍 [搜索执行] 执行X个查询
📝 [报告生成] 开始生成章节报告
```

### 状态追踪
- 章节状态变更追踪
- 智能体对话进度监控
- 搜索执行进度监控
- 错误和异常情况记录

## 未来扩展方向

### 多智能体支持
- 支持更多智能体角色（专家、审核员等）
- 动态智能体分配和调度
- 智能体专业化和角色定制

### 智能化优化
- 基于历史数据的查询优化
- 自动化信息质量评估
- 动态调整对话轮数和策略

### 性能提升
- 并行章节处理
- 智能缓存策略
- 搜索结果去重和合并