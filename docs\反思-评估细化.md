# Deep Research 反思-评估系统完整规范

## 系统概述

Deep Research 的反思-评估系统是一个智能化的研究质量控制机制，通过两阶段分析实现自适应的研究深度控制。

### 核心设计理念
- **反思阶段**：专注当前轮次内容质量分析，评估相对上轮的改进程度
- **评估阶段**：基于反思结果和全局信息，做出继续/停止的战略决策

### 系统架构流程图

```mermaid
graph LR
    A[用户查询] --> B[生成研究计划]
    B --> C[搜索任务执行]
    C --> D[反思分析]
    D --> E[评估决策]
    E --> F{继续研究?}
    F -->|是| G[生成新查询]
    F -->|否| H[研究完成]
    G --> C

    subgraph "反思阶段"
        D1[内容质量分析]
        D2[改进程度评估]
        D3[知识缺口识别]
        D4[生成后续查询]
    end

    subgraph "评估阶段"
        E1[审查反思结果]
        E2[全局覆盖评估]
        E3[资源约束检查]
        E4[决策输出]
    end

    D --> D1 --> D2 --> D3 --> D4
    E --> E1 --> E2 --> E3 --> E4

    style A fill:#e3f2fd
    style H fill:#e8f5e8
    style F fill:#fff3e0
```

## 反思阶段 (Reflection Analysis)

### 功能定位
反思阶段专注于**当前轮次的内容质量分析**，通过对比上一轮的查询目标，评估本轮搜索的改进程度和新发现。

### 输入参数
```typescript
{
  research_topic: string,              // 研究主题
  research_plan: string,               // 原始研究计划
  current_round_findings: string,      // 当前轮详细搜索结果
  previous_queries: string,            // 上一轮查询建议（第一轮为特殊说明）
  previous_recommendation: string,     // 上一轮评估的战略建议
  current_date: string                 // 当前日期
}
```

### 输出结构
```typescript
{
  content_quality_score: number,        // 当前轮内容质量 (0-1)
  knowledge_completeness_score: number, // 当前轮知识完整性 (0-1)
  improvement_analysis: string,         // 相对上轮的改进程度描述 [前端显示]
  key_discoveries: string[],            // 当轮最重要的发现列表 [前端显示]
  knowledge_gaps: string[],             // 新发现的知识缺口列表
  follow_up_queries: Array<{            // 后续查询建议
    query: string,
    language: "chinese" | "english",
    researchGoal: string
  }>
}
```

### 核心分析逻辑
1. **改进评估**：对比上轮查询目标，分析本轮搜索的针对性和效果
2. **质量分析**：评估信息的准确性、可信度、时效性和技术深度
3. **缺口识别**：基于当前发现，识别仍需深入研究的知识领域
4. **查询生成**：针对关键缺口，生成具体可执行的后续查询

### 前端展示
反思任务卡片完整显示：
- `content_quality_score` - 内容质量评分（百分比显示）
- `knowledge_completeness_score` - 知识完整性评分（百分比显示）
- `improvement_analysis` - 改进程度分析（文本段落）
- `key_discoveries` - 关键发现列表（项目符号列表）
- `knowledge_gaps` - 知识缺口列表（项目符号列表）

## 评估阶段 (Strategic Evaluation)

### 功能定位
评估阶段基于**反思结果和全局信息**做出战略决策，综合考虑研究质量、覆盖完整性和资源约束。

### 输入参数
```typescript
{
  research_topic: string,              // 研究主题
  research_plan: string,               // 原始研究计划
  current_reflection: string,          // 当前轮反思结果（JSON字符串）
  all_serp_queries: string,            // 所有轮次查询关键词（逗号分隔）
  all_rounds_discoveries: string,      // 所有轮次关键发现汇总
  current_round: number,               // 当前轮次
  max_rounds: number,                  // 最大轮次限制
  max_duration: number,                // 最长时间限制（分钟）
  quality_threshold: number            // 质量阈值 (0-1)
}
```

### 输出结构
```typescript
{
  should_continue: boolean,            // 是否继续研究
  resource_constraint_reason: string, // 资源限制评估 [前端显示]
  strategic_recommendation: string,   // 全局战略建议 [前端显示]
  coverage_completeness_score: number // 整体覆盖完整性 (0-1)
}
```

### 决策框架
1. **反思分析审查**：评估当前轮次的质量分数和知识缺口重要性
2. **全局覆盖评估**：基于所有查询关键词，判断研究计划的覆盖完整性
3. **效率考量**：分析继续研究的边际收益与资源消耗
4. **约束检查**：考虑轮次限制、时间限制和质量阈值

### 停止条件决策树

```mermaid
flowchart TD
    A[评估开始] --> B{达到最大轮数?}
    B -->|是| C[停止: 达到最大轮数]
    B -->|否| D{超过时间限制?}
    D -->|是| E[停止: 超过时间限制]
    D -->|否| F{覆盖完整性 >= 阈值?}
    F -->|是| G[停止: 达到质量阈值]
    F -->|否| H{边际收益充足?}
    H -->|否| I[停止: 效率不足]
    H -->|是| J[继续: 需要更多研究]

    style C fill:#ffcdd2
    style E fill:#ffcdd2
    style G fill:#c8e6c9
    style I fill:#fff3e0
    style J fill:#e3f2fd
```

### 前端展示
评估任务卡片完整显示：
- `resource_constraint_reason` - 资源限制的客观评估
- `strategic_recommendation` - 基于研究内容的专业建议
- `coverage_completeness_score` - 整体覆盖完整性百分比

## 数据流转机制

### 轮次间数据传递图

```mermaid
flowchart TD
    A[第N轮搜索结果] --> B[反思分析]
    B --> C[评估决策]
    C --> D{继续?}
    D -->|是| E[第N+1轮搜索]
    D -->|否| F[研究结束]
    E --> A

    B --> G[改进分析<br/>关键发现<br/>知识缺口]
    C --> H[决策原因<br/>完整性分数]
    G --> I[下轮反思输入]
    H --> J[流程控制]

    style B fill:#fff3e0
    style C fill:#fce4ec
    style D fill:#ffebee
    style F fill:#e8f5e8
```

### 具体数据流向
1. **反思阶段数据来源**：
   - 当前轮搜索结果（主要分析对象）
   - 上一轮反思的查询建议（改进对比基准）
   - 上一轮评估的战略建议（方向指导）

2. **评估阶段数据来源**：
   - 当前轮反思结果（质量判断依据）
   - 全局查询关键词（覆盖完整性评估）
   - 历史轮次发现汇总（进展追踪）

3. **决策传递机制**：
   - 评估结果 → 研究流程控制（继续/停止）
   - 反思查询 → 下轮搜索任务生成
   - 评估建议 → 下轮反思战略指导

### 任务存储格式
```typescript
// 反思任务
{
  taskType: "reflection",
  learning: `📊 质量评分
- 内容质量: ${content_quality_score * 100}%
- 知识完整性: ${knowledge_completeness_score * 100}%

📈 改进分析
${improvement_analysis}

🔍 关键发现
${key_discoveries.map(item => `• ${item}`).join('\n')}

❓ 知识缺口
${knowledge_gaps.map(gap => `• ${gap}`).join('\n')}`
}

// 评估任务
{
  taskType: "evaluation",
  learning: `🔒 资源限制评估
${resource_constraint_reason}

🎯 战略建议
${strategic_recommendation}

📊 覆盖完整性: ${coverage_completeness_score * 100}%

✅ 整体评估决策: ${should_continue ? '继续研究' : '停止研究'}`
}
```

## 技术实现细节

### 核心算法逻辑

#### 反思阶段实现要点
1. **改进度量**：通过对比上轮查询目标与当轮搜索结果，量化改进程度
2. **质量评估**：多维度评估内容的准确性、深度、时效性和可信度
3. **缺口识别**：基于研究计划和当前发现，系统性识别知识空白
4. **查询优化**：生成针对性强、可执行性高的后续查询建议

#### 评估阶段实现要点
1. **多因子决策**：综合质量分数、覆盖完整性、资源约束的决策模型
2. **阈值管理**：动态调整质量阈值，平衡研究深度与效率
3. **边际分析**：评估额外轮次的预期收益与成本
4. **智能停止**：基于多重条件的智能停止机制

### 提示工程优化
- **上下文管理**：精确控制每阶段的输入信息范围
- **输出格式**：严格的JSON Schema验证确保结构一致性
- **错误处理**：完善的验证和调试机制
- **首轮特殊处理**：针对第一轮无历史数据的特殊逻辑

### 前端集成
- **实时反馈**：研究进展的可视化展示
- **任务卡片**：反思和评估结果的结构化展示
- **决策透明**：详细的决策原因和分析过程
- **用户控制**：保留用户干预和调整的能力

## 系统优势

### 智能化程度
- **自适应深度**：根据研究复杂度自动调整轮次
- **质量保证**：多层次的质量控制机制
- **效率优化**：避免无效搜索和重复研究

### 可解释性
- **决策透明**：每个决策都有明确的理由和分析
- **过程可视**：完整的研究过程记录和展示
- **用户友好**：清晰的界面和反馈机制

### 扩展性
- **模块化设计**：反思和评估模块独立可扩展
- **参数可调**：支持用户自定义质量阈值和轮次限制
- **多模型支持**：兼容不同的AI模型和提供商

## 实际运行示例

### 典型研究流程
以"医学影像主动学习2023-2025年进展"为例：

**第1轮**：
- 搜索：5个初始查询（政策背景、技术概述、应用案例等）
- 反思：内容质量0.78，知识完整性0.65，识别出缺乏顶会论文精读
- 评估：需要继续，生成5个英文学术查询

**第2轮**：
- 搜索：针对MICCAI、CVPR等顶会的精确查询
- 反思：质量提升到0.85，发现理论分析不足
- 评估：覆盖完整性达到阈值0.8，决定停止研究

**结果**：
- 总轮次：2轮
- 停止原因：coverage_threshold_reached
- 研究深度：从政策概述深入到学术前沿
- 时间效率：避免了不必要的第3轮搜索

### 关键成功因素
1. **精确的缺口识别**：反思阶段准确识别知识空白
2. **智能的停止决策**：评估阶段及时识别研究完成点
3. **高质量的查询生成**：后续查询针对性强，搜索效果好
4. **透明的决策过程**：用户可以理解每个决策的原因

---

*本文档记录了Deep Research反思-评估系统的完整设计和实现细节，为系统的进一步优化和扩展提供参考。*
