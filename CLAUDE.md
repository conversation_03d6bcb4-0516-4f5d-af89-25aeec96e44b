# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

Deep Research 是一个基于多种大语言模型的深度研究报告生成平台，支持快速生成综合性研究报告。项目采用 Next.js 15 架构，支持多种AI模型、搜索引擎集成，并提供SSE API和MCP服务。

## 开发命令

### 基础开发
```bash
npm run dev          # 开发服务器 (端口3001)
npm run build        # 生产构建
npm run start        # 启动生产服务器
npm run lint         # ESLint检查
```

### 特殊构建模式
```bash
npm run build:standalone  # 独立部署构建
npm run build:export     # 静态导出构建
```

## 核心架构

### 主要技术栈
- **框架**: Next.js 15 (App Router)
- **UI**: shadcn/ui + Tailwind CSS
- **状态管理**: Zustand
- **AI SDK**: Vercel AI SDK (@ai-sdk/*)
- **类型检查**: TypeScript
- **国际化**: i18next

### 目录结构
```
src/
├── app/                    # Next.js App Router 路由
│   ├── api/               # API 路由
│   │   ├── ai/           # AI 提供商 API 代理
│   │   ├── search/       # 搜索提供商 API
│   │   ├── sse/          # Server-Sent Events API
│   │   └── mcp/          # Model Context Protocol 服务
├── components/            # React 组件
│   ├── Research/         # 研究功能相关组件
│   ├── Knowledge/        # 知识库管理组件
│   ├── MagicDown/        # Markdown 编辑器
│   └── ui/               # shadcn/ui 组件
├── utils/                 # 工具函数
│   └── deep-research/    # 核心研究逻辑
├── hooks/                # 自定义 Hooks
├── store/                # Zustand 状态管理
├── types/                # TypeScript 类型定义
└── constants/            # 常量定义
```

### 核心研究流程
研究系统基于 `DeepResearch` 类实现，支持以下流程：

1. **研究计划生成** (`writeReportPlan()`)
2. **SERP查询生成** (`generateSERPQuery()`) 
3. **搜索任务执行** (`runSearchTask()`)
4. **最终报告生成** (`writeFinalReport()`)

### AI 模型集成
系统支持多个AI提供商，位于 `src/app/api/ai/` 目录：
- Google Gemini
- OpenAI/Azure OpenAI
- Anthropic Claude
- DeepSeek
- Mistral
- Ollama (本地)
- OpenRouter
- XAI (Grok)

### 搜索引擎集成
支持多个搜索提供商，位于 `src/app/api/search/` 目录：
- Tavily
- Firecrawl
- Exa
- SearXNG
- Bocha

### 状态管理
使用 Zustand 管理全局状态：
- `useGlobalStore`: 全局UI状态
- `useSettingStore`: 用户设置
- `useHistoryStore`: 研究历史
- `useKnowledgeStore`: 本地知识库
- `useTaskStore`: 任务管理

### 关键 Hooks
- `useDeepResearch`: 核心研究功能
- `useAutoResearch`: 自动研究功能
- `useChapterResearch`: 章节研究功能
- `useAiProvider`: AI 提供商管理
- `useWebSearch`: 搜索功能


## 对抗性计划优化功能

### 功能概述
对抗性计划优化是一个基于多轮对话的研究计划生成和优化系统，通过"架构师"（Architect）和"批判者"（Critic）两个AI角色的对抗性对话来迭代优化研究大纲。该功能作为可选的高级计划优化流程，在用户确认核心研究问题后启动，旨在产出结构更严谨、内容更全面、逻辑更清晰的高质量研究计划。该功能分支并行与原始的直接一步生成plan的流程。

### 核心文件结构

#### 状态管理层
- `src/store/task.ts`: 对抗性计划状态管理
  - `AdversarialRound`: 对话轮次数据结构，包含轮次号、角色、思考过程、动作类型、计划内容、批判意见、结束理由和时间戳
  - `AdversarialPlanState`: 计划优化完整状态，管理当前轮次、最大轮次、运行状态、对话历史等
  - 支持propose_plan（计划提议）、critique_plan（计划批判）、finish_discussion（结束讨论）三种核心动作
  - 集成到全局useTaskStore中，与其他研究功能无缝协作

#### 前端组件层
- `src/components/Research/AdversarialPlanOptimizer.tsx`: 对抗性计划优化主组件
  - 嵌入式非阻塞UI设计，不影响主研究流程
  - 支持自动模式（AI自主决定结束）和手动模式（用户控制轮次）
  - 实时进度跟踪：当前轮次/最大轮次、当前角色、运行状态
  - 双视图模式：原始日志（开发调试）和美观对话视图（用户友好）
  - 收缩/展开状态管理，显示优化进度和完成状态

#### 核心逻辑层
- `src/hooks/useTaskStore.ts`: 对抗性计划Hook集成
  - 管理对抗性计划的完整生命周期
  - 处理架构师和批判者的轮换逻辑
  - 提供计划启动、停止、重置等控制方法
  - 集成AI提供商调用和错误处理

### 工作流程

#### 1. 初始化阶段
- 用户输入研究主题并确认后，可选择启动对抗性计划优化
- 系统初始化架构师和批判者状态
- 设置优化参数：最大轮次、模式选择（自动/手动）
- 显示优化启动提示和目标说明

#### 2. 计划提议阶段（架构师）
- 架构师基于研究主题生成初始研究计划
- 计划结构包含：
  - section_title: 章节标题
  - summary: 章节内容摘要
- 考虑因素：
  - 主题相关性和覆盖完整性
  - 逻辑结构和层次关系
  - 研究深度和广度平衡
  - 可执行性和实用性

#### 3. 批判优化阶段（批判者）
- 批判者从六个核心维度评估计划：
  - **主题相关性（topic_relevance）**: 评估各章节与研究主题的关联度
  - **完整性（completeness）**: 识别遗漏的重要方面和知识点
  - **非冗余性（non_redundancy）**: 检查重复内容和不必要的章节
  - **简洁性（conciseness）**: 评估表达的清晰度和精炼程度
  - **抽象指导（abstraction_guidance）**: 检查抽象层次和指导价值
  - **逻辑流程（logical_flow）**: 评估章节间的逻辑关系和阅读流畅性
- 生成结构化批判意见，包含具体改进建议
- 决定是否需要进一步优化或可以结束讨论

#### 4. 迭代优化循环
- **自动模式**: AI自主判断优化质量，达到满意程度时自动结束
- **手动模式**: 用户预设轮次数量，到达后停止或手动干预
- **强制结束机制**: 防止无限循环，确保系统稳定性
- **版本追踪**: 保留每轮优化的计划版本，支持对比和回滚

#### 5. 优化完成
- 生成最终优化计划，自动设置为当前研究计划
- 提供优化历史回顾和改进总结
- 支持计划导出和手动编辑
- 无缝衔接到章节式研究或标准研究流程

### 显示特性

#### 状态可视化
- 实时显示当前轮次进度（Round X / Y）
- 角色状态指示：当前是架构师还是批判者在思考
- 运行状态展示：思考中、等待中、已完成
- 优化完成标识和成功提示

#### 对话历史展示
- 按轮次分组显示，每轮包含架构师提议和批判者反馈
- 角色区分：不同颜色和图标标识架构师/批判者
- 动作类型标识：propose_plan、critique_plan、finish_discussion
- 内容结构化展示：
  - 思考过程（thought）
  - 计划内容（plan数组）
  - 批判意见（critique对象）
  - 结束理由（finish_reason）

#### 用户体验优化
- 收缩状态显示优化进度和当前状态
- 展开状态提供完整对话历史和控制选项
- 原始日志模式：开发者友好的JSON格式显示
- 美观视图模式：用户友好的卡片式对话展示
- 实时更新：无需刷新页面即可看到优化进展

#### 交互控制
- 启动/停止优化控制
- 模式切换：自动模式 ↔ 手动模式
- 轮次调整：动态修改最大轮次限制
- 计划导出：支持Markdown格式导出优化后的计划



## 技术架构集成

### 研究流程协调
Deep Research 支持三种主要研究模式的无缝切换：

#### 1. 标准流程 → 原始一步生成plan/对抗性计划优化
- 用户输入研究主题后可选择启动对抗性计划优化
- 优化完成的计划可直接用于后续研究
- 支持计划导出和手动编辑

#### 2. 原始一步生成plan/对抗性计划优化 → 原始一步信息收集研究/章节式研究信息收集
- 优化后的计划自动解析为章节列表
- 每个章节成为独立的研究单元
- 智能体对话基于章节目标进行

#### 3. 原始一步信息收集研究/章节式研究信息收集 → 最终报告
- 智能体达成的查询共识自动执行
- 查询结果集成到信息收集系统
- 支持多源搜索和结果聚合

### 状态管理架构

#### 全局状态 (Zustand)
- `src/store/task.ts`: 任务和对抗性计划状态
- `src/store/setting.ts`: 用户设置和偏好
- `src/store/global.ts`: 全局UI状态

#### 组件级状态
- React useState: 组件内部状态
- useCallback: 性能优化和依赖管理
- useEffect: 生命周期和副作用处理

### AI 提供商集成

#### 支持的AI服务
- OpenAI (GPT系列)
- Anthropic (Claude系列)
- Google (Gemini系列)
- 本地模型 (Ollama)
- 兼容OpenAI API的服务

#### 提供商管理
- `src/hooks/useAiProvider.ts`: AI提供商抽象层
- 统一的API调用接口
- 自动错误处理和重试机制
- 支持流式响应和实时更新

### 搜索引擎集成

#### 支持的搜索服务
- Tavily: 专业研究搜索
- Exa: 语义搜索
- SearXNG: 开源元搜索
- Firecrawl: 网页内容提取
- Bocha: 自定义搜索

#### 搜索策略
- 双语搜索支持（中英文）
- 多源结果聚合
- 智能去重和排序
- 图片和链接提取

## 环境配置

### 开发环境设置
1. 复制 `env.tpl` 到 `.env.local` (开发) 或 `.env` (生产)
2. 配置所需的API密钥和基础URL
3. 运行 `npm run dev` 启动开发服务器

### 关键环境变量
- `ACCESS_PASSWORD`: 服务器访问密码
- `GOOGLE_GENERATIVE_AI_API_KEY`: Gemini API密钥
- `OPENAI_API_KEY`: OpenAI API密钥
- `ANTHROPIC_API_KEY`: Claude API密钥
- `TAVILY_API_KEY`: Tavily搜索API密钥
- `NEXT_PUBLIC_BUILD_MODE`: 构建模式

## PWA 和部署

### 支持的部署平台
- Vercel (推荐)
- Cloudflare Pages
- Docker
- 静态部署

### PWA 配置
使用 Serwist 实现 PWA 功能，配置位于：
- `src/app/sw.ts`: Service Worker
- `src/app/manifest.json`: Web App Manifest

## 国际化

支持多语言：
- 中文 (zh-CN)
- 英文 (en-US) 
- 西班牙文 (es-ES)
- 越南文 (vi-VN)

语言文件位于 `src/locales/` 目录

## API 服务

### Server-Sent Events API
- 端点: `/api/sse`
- 支持实时研究进度推送
- GET/POST 两种调用方式

### Model Context Protocol (MCP)
- HTTP 端点: `/api/mcp`
- SSE 端点: `/api/mcp/sse`
- 支持作为MCP服务器运行

## 开发注意事项

### 代码风格
- 使用 TypeScript 严格模式
- 遵循 ESLint 配置规则
- 使用 shadcn/ui 组件规范

### 测试和构建
- 修改代码后运行 `npm run lint` 检查
- 构建前确保类型检查通过
- 使用 `npm run build` 验证生产构建

### 性能优化
- 启用 React Compiler (`experimental.reactCompiler: true`)
- 使用 Turbo 模式开发 (`--turbopack`)
- 支持代码分割和懒加载

### 文档和资源
- README.md: 用户使用指南
- docs/ 目录: 详细技术文档
- 支持 Docker 和 docker-compose 部署