# 双语查询功能测试指南

## 功能概述
实现了Deep Research项目的双语查询功能，允许用户在中文和英文查询词之间切换。

## 主要变更

### 1. 数据结构扩展
- **SearchTask接口**：添加了`englishQuery?`字段来存储英文查询词
- **设置存储**：添加了`queryLanguage: "chinese" | "english"`偏好设置

### 2. AI提示词优化
- **serpQueriesPrompt**：修改提示词，要求AI同时生成中英文版本的查询词
- **JSON Schema**：更新schema以支持双语查询词输出

### 3. 前端UI组件
- **QueryLanguageToggle**：新增语言切换按钮组件
- **SearchResult**：集成语言切换功能，根据用户选择显示对应查询词

### 4. 搜索逻辑优化
- **runSearchTask**：根据用户语言偏好选择实际搜索使用的查询词
- **AI处理**：确保AI处理搜索结果时也使用正确的查询词

## 测试步骤

### 1. 基础功能测试
1. 启动项目：`npm run dev`
2. 输入一个中文研究主题，如："人工智能在医疗领域的应用"
3. 点击"开始思考"生成研究问题
4. 点击"撰写报告方案"生成报告计划
5. 点击"开始研究"生成搜索查询

### 2. 双语查询词验证
1. 在搜索结果区域，查看生成的查询词是否包含中英文版本
2. 检查AI是否按照新的提示词生成了双语查询词
3. 验证JSON输出格式是否正确

### 3. 语言切换功能测试
1. 在搜索结果标题栏找到语言切换按钮（Languages图标）
2. 点击按钮，选择"English"
3. 观察查询词显示是否切换为英文版本
4. 切换回"中文"，验证显示是否恢复为中文查询词

### 4. 搜索执行测试
1. 在不同语言设置下，点击"重新研究"按钮
2. 验证实际搜索是否使用了正确语言的查询词
3. 检查搜索结果的质量差异

## 预期结果

### 成功指标
- ✅ AI能够同时生成中英文查询词
- ✅ 前端正确显示语言切换按钮
- ✅ 用户可以在中英文查询词之间切换显示
- ✅ 搜索执行时使用正确语言的查询词
- ✅ 语言偏好设置能够持久化保存

### 可能的问题
- ❌ AI可能不总是生成高质量的英文查询词
- ❌ 某些查询词可能没有合适的英文对应
- ❌ 类型错误（由于依赖包版本问题，但不影响功能）

## 后续优化建议

1. **查询词质量优化**：
   - 添加查询词质量评估机制
   - 提供手动编辑查询词的功能

2. **用户体验改进**：
   - 添加查询词预览功能
   - 提供批量语言切换选项

3. **性能优化**：
   - 缓存已生成的双语查询词
   - 优化搜索API调用

4. **国际化完善**：
   - 添加更多语言的国际化文本
   - 支持更多语言的查询词生成

## 技术实现细节

### 关键文件修改
- `src/types.d.ts`：扩展SearchTask接口
- `src/constants/prompts.ts`：修改serpQueriesPrompt
- `src/utils/deep-research/prompts.ts`：更新JSON schema
- `src/store/setting.ts`：添加语言偏好设置
- `src/components/Internal/QueryLanguageToggle.tsx`：新增语言切换组件
- `src/components/Research/SearchResult.tsx`：集成语言切换功能
- `src/hooks/useDeepResearch.ts`：更新搜索逻辑
- `src/locales/zh-CN.json`：添加国际化文本

### 核心逻辑
```typescript
// 根据用户语言偏好获取显示的查询词
function getDisplayQuery(item: SearchTask) {
  if (queryLanguage === "english" && item.englishQuery) {
    return item.englishQuery;
  }
  return item.query; // 默认显示中文查询词
}

// 根据用户语言偏好获取搜索使用的查询词
function getSearchQuery(item: SearchTask) {
  if (queryLanguage === "english" && item.englishQuery) {
    return item.englishQuery;
  }
  return item.query; // 默认使用中文查询词
}
```

这个实现确保了用户可以灵活地在中英文查询词之间切换，同时保持了向后兼容性。
