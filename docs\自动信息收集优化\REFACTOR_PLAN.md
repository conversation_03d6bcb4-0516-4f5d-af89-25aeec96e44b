# 章节式研究系统重构实施计划\n\n**核心目标：** 废除旧的“反思-评估”机制，实现一个全新的、基于双AI代理辩论的、上下文严格隔离的章节式研究系统。\n\n**核心教训与对策：**\n1.  **问题：** `Edit`和`Write`工具因字符串转义问题导致反复失败。\n2.  **对策：** **最小化和隔离文件修改**。尽可能创建新文件，而不是修改现有文件。对于必须的修改（如最终集成），将采用更原子化的`MultiEdit`工具，或在您允许的情况下，提供清晰的、可供您手动执行的指令。\n3.  **问题：** 实现步骤过于宏大，导致错误难以定位。\n4.  **对策：** **步步为营，及时验证**。在每一步创建或修改文件后，都将立即执行`npm run build`命令，确保在进入下一步之前，项目处于可编译的健康状态。\n\n---\n\n## 详细实施步骤\n\n### 第一阶段：奠定数据与状态基础\n\n1.  **任务1：创建核心类型定义**\n    *   **操作：** 在`src/utils/chapter-research/`目录下，创建`types.ts`文件。\n    *   **内容：** 定义所有与章节研究相关的TypeScript接口和类型（如`Chapter`, `AgentAction`, `DebateHistory`等）。\n    *   **验证：** `npm run build`\n\n2.  **任务2：创建全局状态管理器**\n    *   **操作：** 在`src/store/`目录下，创建`chapter-research.ts`文件。\n    *   **内容：** 使用Zustand创建`useChapterResearchStore`，管理整个章节研究流程的状态。\n    *   **验证：** `npm run build`\n\n### 第二阶段：构建核心后端逻辑\n\n3.  **任务3：实现公共工具与提示词**\n    *   **操作：** 在`src/utils/chapter-research/`下创建`consensusValidator.ts`和`prompts.ts`。\n    *   **内容：** `consensusValidator.ts`将包含检查AI共识的静态方法。`prompts.ts`将包含生成AI辩论提示的函数。\n    *   **验证：** `npm run build`\n\n4.  **任务4：实现核心引擎**\n    *   **操作：** 在`src/utils/chapter-research/`下创建`contextIsolator.ts`和`agentDebateEngine.ts`。\n    *   **内容：** 实现上下文隔离器和双AI辩论引擎的核心逻辑。\n    *   **验证：** `npm run build`\n\n5.  **任务5：实现顶层管理器**\n    *   **操作：** 在`src/utils/chapter-research/`下创建`manager.ts`。\n    *   **内容：** 实现`ChapterResearchManager`，作为整个流程的协调器，串联起前面创建的所有模块。\n    *   **验证：** `npm run build`\n\n### 第三阶段：UI呈现与最终集成\n\n6.  **任务6：开发UI组件**\n    *   **操作：** 在`src/components/Research/`下创建`ChapterResearch`目录，并在其中创建`ChapterProgress.tsx`和`DebateView.tsx`组件。\n    *   **内容：** 开发用于可视化研究进度和AI辩论过程的React组件。\n    *   **验证：** `npm run build`\n\n7.  **任务7：集成到主页面**\n    *   **操作：** 这是唯一需要修改现有文件的步骤。我将以最安全的方式，提供精确的指令来修改`src/components/Research/Topic.tsx`。\n    *   **内容：**\n        1.  添加新的`import`语句。\n        2.  扩展`formSchema`以包含研究模式切换。\n        3.  更新`handleSubmit`函数，根据模式调用新或旧的研究流程。\n        4.  在JSX中添加UI开关和新的UI组件。\n    *   **验证：** `npm run build`\n