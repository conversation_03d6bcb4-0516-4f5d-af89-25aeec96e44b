/**
 * Report type definitions for multiple report format functionality
 */

export type ReportType = 'standard' | 'collateral-analysis' | 'test' |'custom';
export type PlanWeight = 'strong' | 'weak' | 'none';

export interface ReportConfig {
  type: ReportType;
  planWeight: PlanWeight;
  name: string;
  description: string;
  icon?: string;
}

export interface CustomReportFormat {
  id: string;
  name: string;
  description: string;
  prompt: string;
  planWeight: PlanWeight;
  createdAt: string;
  updatedAt: string;
}

export const REPORT_CONFIGS: Record<ReportType, ReportConfig> = {
  standard: {
    type: 'standard',
    planWeight: 'strong',
    name: 'Standard Report',
    description: 'Comprehensive research report following the original plan structure',
    icon: '📄'
  },
  'collateral-analysis': {
    type: 'collateral-analysis',
    planWeight: 'weak',
    name: 'Collateral Analysis',
    description: 'Financial collateral analysis report format for institutional research',
    icon: '📊'
  },
  'test': {
    type: 'test',
    planWeight: 'none',
    name: 'Test Report',
    description: 'Test report format for testing purposes',
    icon: '🧪'
  },
  custom: {
    type: 'custom',
    planWeight: 'weak',
    name: 'Custom Format',
    description: 'User-defined custom report format with personalized prompt',
    icon: '✏️'
  }
};

export const DEFAULT_CUSTOM_FORMATS: CustomReportFormat[] = [
  {
    id: 'executive-summary',
    name: 'Executive Summary',
    description: 'Concise executive summary format for decision makers',
    prompt: `Write an executive summary format report with the following structure:

## **Executive Summary**
- 2-3 paragraphs summarizing core findings
- Key insights and implications

## **Key Findings**
- 3-5 bullet points with data support
- Most important discoveries from research

## **Recommendations**
- 3-5 actionable recommendations
- Specific next steps for implementation

## **Supporting Evidence**
- Brief data points and sources
- Key statistics and metrics

## **Next Steps**
- Concrete action items
- Timeline considerations

Use concise, powerful language suitable for executives and decision makers.`,
    planWeight: 'weak',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'academic-paper',
    name: 'Academic Paper',
    description: 'Academic research paper format with formal structure',
    prompt: `Write an academic research paper format with the following structure:

## **Abstract**
- 150-200 words summarizing purpose, methods, findings, and conclusions

## **1. Introduction**
- Research background and context
- Problem statement and research questions
- Objectives and scope

## **2. Literature Review**
- Current state of research
- Theoretical framework
- Research gaps identified

## **3. Methodology**
- Data collection methods
- Analysis approach
- Research limitations

## **4. Findings and Analysis**
- Detailed presentation of results
- Data analysis and interpretation
- Supporting evidence and examples

## **5. Discussion**
- Interpretation of findings
- Implications and significance
- Comparison with existing research

## **6. Conclusion**
- Summary of key contributions
- Practical implications
- Future research directions

## **References**
- Cited sources and materials

Use formal academic writing style with rigorous analysis and proper citations.`,
    planWeight: 'weak',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

export const PLAN_WEIGHT_DESCRIPTIONS: Record<PlanWeight, string> = {
  strong: 'Strictly follow the original research plan structure',
  weak: 'Reference the original plan but prioritize the format requirements',
  none: 'Ignore the original plan and focus entirely on the new format'
};
