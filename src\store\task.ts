import { create } from "zustand";
import { persist } from "zustand/middleware";
import { pick } from "radash";
import { ReportType, CustomReportFormat, DEFAULT_CUSTOM_FORMATS } from "@/types/report";
import {
  AutoResearchState,
  DEFAULT_AUTO_RESEARCH_STATE,
  AutoResearchConfig,
  validateAutoResearchConfig,
} from "@/types/auto-research";
import {
  ChapterResearchState,
  DEFAULT_CHAPTER_RESEARCH_STATE,
  ChapterInfo,
  AgentDiscussion,
  ChapterCollectedInfo,
  ChapterReport,
  ChapterResearchConfig,
  SearchProgress,
  GlobalCitation,
} from "@/types/chapter-research";

// 对抗机制相关类型定义
export interface AdversarialRound {
  roundNumber: number;
  persona: '架构师' | '批判者';
  thought: string;
  action: 'propose_plan' | 'critique_plan' | 'finish_discussion';
  plan?: Array<{
    section_title: string;
    summary: string;
  }>;
  critique?: string | {
    topic_relevance?: string[];
    completeness?: string[];
    non_redundancy?: string[];
    conciseness?: string[];
    abstraction_guidance?: string[];
    logical_flow?: string[];
  };
  finish_reason?: string;
  timestamp: number;
}


export interface AdversarialPlanState {
  rounds: AdversarialRound[];
  currentRound: number;
  maxRounds: number;
  mode: 'fixed' | 'auto';
  isActive: boolean;
  isRunning: boolean;
  currentPersona: '架构师' | '批判者';
  conversationText: string;
}

export const DEFAULT_ADVERSARIAL_STATE: AdversarialPlanState = {
  rounds: [],
  currentRound: 0,
  maxRounds: 5,
  mode: 'auto',
  isActive: false,
  isRunning: false,
  currentPersona: '架构师',
  conversationText: '',
};


export interface TaskStore {
  id: string;
  question: string;
  resources: Resource[];
  query: string;
  questions: string;
  feedback: string;
  reportPlan: string;
  suggestion: string;
  tasks: SearchTask[];
  requirement: string;
  title: string;
  finalReport: string;
  htmlContent: string;
  sources: Source[];
  images: ImageSource[];
  knowledgeGraph: string;
  // Report format related fields
  reportType: ReportType;
  customPrompt: string;
  customFormats: CustomReportFormat[];
  selectedCustomFormatId: string | null;
  // Auto research related fields
  autoResearch: AutoResearchState;
  // Adversarial plan optimization related fields (optional)
  adversarialPlan?: AdversarialPlanState;
  // Chapter research related fields
  chapterResearch: ChapterResearchState;
}

interface TaskFunction {
  update: (tasks: SearchTask[]) => void;
  setId: (id: string) => void;
  setTitle: (title: string) => void;
  setSuggestion: (suggestion: string) => void;
  setRequirement: (requirement: string) => void;
  setQuery: (query: string) => void;
  updateTask: (query: string, task: Partial<SearchTask>) => void;
  removeTask: (query: string, language?: "chinese" | "english") => boolean;
  canDeleteTask: (query: string, language?: "chinese" | "english") => boolean;
  setQuestion: (question: string) => void;
  addResource: (resource: Resource) => void;
  updateResource: (id: string, resource: Partial<Resource>) => void;
  removeResource: (id: string) => boolean;
  updateQuestions: (questions: string) => void;
  updateReportPlan: (plan: string) => void;
  updateFinalReport: (report: string) => void;
  updateHtmlContent: (htmlContent: string) => void;
  setSources: (sources: Source[]) => void;
  setImages: (images: Source[]) => void;
  setFeedback: (feedback: string) => void;
  updateKnowledgeGraph: (knowledgeGraph: string) => void;
  clear: () => void;
  reset: () => void;
  backup: () => TaskStore;
  restore: (taskStore: TaskStore) => void;
  // Report format related functions
  setReportType: (reportType: ReportType) => void;
  setCustomPrompt: (prompt: string) => void;
  addCustomFormat: (format: Omit<CustomReportFormat, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateCustomFormat: (id: string, format: Partial<CustomReportFormat>) => void;
  removeCustomFormat: (id: string) => void;
  setSelectedCustomFormatId: (id: string | null) => void;
  // Auto research related functions
  updateAutoResearchConfig: (config: Partial<AutoResearchConfig>) => void;
  setAutoResearchRunning: (isRunning: boolean) => void;
  setAutoResearchCurrentRound: (round: number) => void;
  // 轮次相关函数已被删除，现在使用章节研究机制
  setAutoResearchShowConfig: (show: boolean) => void;
  resetAutoResearch: () => void;
  addAutoResearchTask: (task: SearchTask) => void;
  // Adversarial plan optimization related functions
  initAdversarialPlan: (maxRounds: number, mode: 'fixed' | 'auto') => void;
  setAdversarialPlanRunning: (isRunning: boolean) => void;
  addAdversarialRound: (round: AdversarialRound) => void;
  updateAdversarialPersona: (persona: '架构师' | '批判者') => void;
  updateAdversarialConversationText: (text: string) => void;
  finishAdversarialPlan: (finalPlan: string) => void;
  resetAdversarialPlan: () => void;
  // Chapter research related functions
  initChapterResearch: (userQuery: string, chapters: ChapterInfo[]) => void;
  setChapterResearchRunning: (isRunning: boolean) => void;
  setChapterResearchShowConfig: (show: boolean) => void;
  updateChapterResearchConfig: (config: Partial<ChapterResearchConfig>) => void;
  setCurrentChapter: (chapterId: string) => void;
  updateChapterStatus: (chapterId: string, status: ChapterInfo['status']) => void;
  updateChapterPhase: (chapterId: string, phase: ChapterInfo['currentPhase']) => void;
  setChapterConsensus: (chapterId: string, consensus: string) => void;
  setChapterQueries: (chapterId: string, queries: Array<{query: string; researchGoal: string}>) => void;
  updateSearchProgress: (chapterId: string, progress: Partial<SearchProgress>) => void;
  addAgentDiscussion: (discussion: AgentDiscussion) => void;
  addChapterCollectedInfo: (info: ChapterCollectedInfo) => void;
  addChapterReport: (report: ChapterReport) => void;
  resetChapterResearch: () => void;
  // 新增历史数据管理方法
  addChapterHistoricalConsensus: (chapterId: string, consensus: string) => void;
  addChapterHistoricalQueries: (chapterId: string, queries: Array<{query: string; researchGoal: string; fromRound: number}>) => void;
  clearChapterHistoricalData: (chapterId: string) => void;
  // 全局引用管理方法
  registerGlobalCitation: (source: Source, chapterId?: string, roundNumber?: number) => number;
  registerMultipleCitations: (sources: Source[], chapterId?: string, roundNumber?: number) => Map<string, number>;
  getGlobalCitationByUrl: (url: string) => GlobalCitation | undefined;
  getAllGlobalCitations: () => GlobalCitation[];
  // 第一章节预览报告方法
  setFirstChapterPreview: (preview: string) => void;
}

const defaultValues: TaskStore = {
  id: "",
  question: "",
  resources: [],
  query: "",
  questions: "",
  feedback: "",
  reportPlan: "",
  suggestion: "",
  tasks: [],
  requirement: "",
  title: "",
  finalReport: "",
  htmlContent: "",
  sources: [],
  images: [],
  knowledgeGraph: "",
  // Report format related default values
  reportType: "standard",
  customPrompt: "",
  customFormats: DEFAULT_CUSTOM_FORMATS,
  selectedCustomFormatId: null,
  // Auto research related default values
  autoResearch: DEFAULT_AUTO_RESEARCH_STATE,
  // Chapter research related default values
  chapterResearch: DEFAULT_CHAPTER_RESEARCH_STATE,
};

export const useTaskStore = create(
  persist<TaskStore & TaskFunction>(
    (set, get) => ({
      ...defaultValues,
      update: (tasks) => set(() => ({ tasks: [...tasks] })),
      setId: (id) => set(() => ({ id })),
      setTitle: (title) => set(() => ({ title })),
      setSuggestion: (suggestion) => set(() => ({ suggestion })),
      setRequirement: (requirement) => set(() => ({ requirement })),
      setQuery: (query) => set(() => ({ query })),
      updateTask: (query, task) => {
        const newTasks = get().tasks.map((item) => {
          return item.query === query ? { ...item, ...task } : item;
        });
        set(() => ({ tasks: [...newTasks] }));
      },
      removeTask: (query, language) => {
        set((state) => ({
          tasks: state.tasks.filter((task) => {
            // 如果指定了language，则同时匹配query和language
            if (language) {
              return !(task.query === query && task.language === language);
            }
            // 如果没有指定language，则只匹配query（向后兼容）
            return task.query !== query;
          }),
        }));
        return true;
      },
      canDeleteTask: (query, language) => {
        const task = get().tasks.find((task) => {
          if (language) {
            return task.query === query && task.language === language;
          }
          return task.query === query;
        });
        // 只有非processing状态的任务才能删除
        return task ? task.state !== "processing" : false;
      },
      setQuestion: (question) => set(() => ({ question })),
      addResource: (resource) =>
        set((state) => ({ resources: [resource, ...state.resources] })),
      updateResource: (id, resource) => {
        const newResources = get().resources.map((item) => {
          return item.id === id ? { ...item, ...resource } : item;
        });
        set(() => ({ resources: [...newResources] }));
      },
      removeResource: (id) => {
        set((state) => ({
          resources: state.resources.filter((resource) => resource.id !== id),
        }));
        return true;
      },
      updateQuestions: (questions) => set(() => ({ questions })),
      updateReportPlan: (plan) => set(() => ({ reportPlan: plan })),
      updateFinalReport: (report) => set(() => ({ finalReport: report })),
      updateHtmlContent: (htmlContent) => set(() => ({ htmlContent })),
      setSources: (sources) => set(() => ({ sources })),
      setImages: (images) => set(() => ({ images })),
      setFeedback: (feedback) => set(() => ({ feedback })),
      updateKnowledgeGraph: (knowledgeGraph) => set(() => ({ knowledgeGraph })),
      clear: () => set(() => ({ tasks: [] })),
      reset: () => set(() => ({ 
        ...defaultValues,
        // 确保对抗性计划状态完全清除
        adversarialPlan: undefined
      })),
      backup: () => {
        return {
          ...pick(get(), Object.keys(defaultValues) as (keyof TaskStore)[]),
        } as TaskStore;
      },
      restore: (taskStore) => set(() => ({ ...taskStore })),
      // Report format related function implementations
      setReportType: (reportType) => set(() => ({ reportType })),
      setCustomPrompt: (customPrompt) => set(() => ({ customPrompt })),
      addCustomFormat: (format) => {
        const newFormat: CustomReportFormat = {
          ...format,
          id: `custom-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        set((state) => ({
          customFormats: [...state.customFormats, newFormat],
        }));
      },
      updateCustomFormat: (id, format) => {
        set((state) => ({
          customFormats: state.customFormats.map((item) =>
            item.id === id
              ? { ...item, ...format, updatedAt: new Date().toISOString() }
              : item
          ),
        }));
      },
      removeCustomFormat: (id) => {
        set((state) => ({
          customFormats: state.customFormats.filter((format) => format.id !== id),
          selectedCustomFormatId:
            state.selectedCustomFormatId === id ? null : state.selectedCustomFormatId,
        }));
      },
      setSelectedCustomFormatId: (selectedCustomFormatId) =>
        set(() => ({ selectedCustomFormatId })),
      // Auto research related function implementations
      updateAutoResearchConfig: (config) => {
        set((state) => ({
          autoResearch: {
            ...state.autoResearch,
            config: validateAutoResearchConfig({
              ...state.autoResearch.config,
              ...config,
            }),
          },
        }));
      },
      setAutoResearchRunning: (isRunning) => {
        set((state) => ({
          autoResearch: {
            ...state.autoResearch,
            runtime: {
              ...state.autoResearch.runtime,
              isRunning,
              startTime: isRunning ? Date.now() : state.autoResearch.runtime.startTime,
            },
          },
        }));
      },
      setAutoResearchCurrentRound: (currentRound) => {
        set((state) => ({
          autoResearch: {
            ...state.autoResearch,
            runtime: {
              ...state.autoResearch.runtime,
              currentRound,
            },
          },
        }));
      },
      // 轮次相关函数实现已被删除
      setAutoResearchShowConfig: (showConfig) => {
        set((state) => ({
          autoResearch: {
            ...state.autoResearch,
            showConfig,
          },
        }));
      },
      resetAutoResearch: () => {
        set(() => ({
          autoResearch: DEFAULT_AUTO_RESEARCH_STATE,
        }));
      },
      addAutoResearchTask: (task) => {
        console.log('📝 [任务创建] 添加搜索任务:', {
          query: task.query.substring(0, 50),
          taskType: task.taskType,
          state: task.state,
          chapterId: (task as any).chapterId,
          roundNumber: (task as any).roundNumber,
          timestamp: Date.now()
        });
        
        set((state) => {
          const newTasks = [...state.tasks, task];
          console.log(`📊 [任务统计] 任务总数: ${newTasks.length}, 新任务类型: ${task.taskType}`);
          
          return {
            tasks: newTasks,
          };
        });
      },
      // Adversarial plan optimization related function implementations
      initAdversarialPlan: (maxRounds, mode) => {
        set(() => ({
          adversarialPlan: {
            ...DEFAULT_ADVERSARIAL_STATE,
            maxRounds,
            mode,
            isActive: true,
            currentPersona: '架构师',
          },
        }));
      },
      setAdversarialPlanRunning: (isRunning) => {
        set((state) => ({
          adversarialPlan: state.adversarialPlan ? {
            ...state.adversarialPlan,
            isRunning,
          } : undefined,
        }));
      },
      addAdversarialRound: (round) => {
        set((state) => ({
          adversarialPlan: state.adversarialPlan ? {
            ...state.adversarialPlan,
            rounds: [...state.adversarialPlan.rounds, round],
            currentRound: round.roundNumber,
          } : undefined,
        }));
      },
      updateAdversarialPersona: (persona) => {
        set((state) => ({
          adversarialPlan: state.adversarialPlan ? {
            ...state.adversarialPlan,
            currentPersona: persona,
          } : undefined,
        }));
      },
      updateAdversarialConversationText: (text) => {
        set((state) => ({
          adversarialPlan: state.adversarialPlan ? {
            ...state.adversarialPlan,
            conversationText: text,
          } : undefined,
        }));
      },
      finishAdversarialPlan: (finalPlan) => {
        set((state) => ({
          reportPlan: finalPlan,
          adversarialPlan: state.adversarialPlan ? {
            ...state.adversarialPlan,
            isRunning: false,
            // isActive 保持 true 以便显示最终结果
          } : undefined,
        }));
      },
      resetAdversarialPlan: () => {
        set(() => ({
          adversarialPlan: undefined,
        }));
      },
      // Chapter research related function implementations
      initChapterResearch: (userQuery, chapters) => {
        set((state) => ({
          chapterResearch: {
            ...DEFAULT_CHAPTER_RESEARCH_STATE,
            isActive: true,
            showConfig: state.chapterResearch.showConfig, // 保持当前配置面板显示状态
            // 保留现有的全局引用状态，避免重置
            globalCitations: state.chapterResearch.globalCitations,
            globalCitationCounter: state.chapterResearch.globalCitationCounter,
            userQuery,
            chapters: chapters.map((chapter, index) => ({
              ...chapter,
              status: index === 0 ? 'pending' : 'pending',
              currentRound: 0,
              currentPhase: 'high_level',
            })),
          },
        }));
      },
      setChapterResearchRunning: (isRunning) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            isRunning,
          },
        }));
      },
      setChapterResearchShowConfig: (show) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            showConfig: show,
          },
        }));
      },
      updateChapterResearchConfig: (config) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            config: {
              ...state.chapterResearch.config,
              ...config,
            },
          },
        }));
      },
      setCurrentChapter: (chapterId) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            currentChapterId: chapterId,
          },
        }));
      },
      updateChapterStatus: (chapterId, status) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            chapters: state.chapterResearch.chapters.map((chapter) =>
              chapter.id === chapterId ? { ...chapter, status } : chapter
            ),
          },
        }));
      },
      updateChapterPhase: (chapterId, phase) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            chapters: state.chapterResearch.chapters.map((chapter) =>
              chapter.id === chapterId ? { ...chapter, currentPhase: phase } : chapter
            ),
          },
        }));
      },
      setChapterConsensus: (chapterId, consensus) => {
        set((state) => {
          const updatedState = {
            chapterResearch: {
              ...state.chapterResearch,
              chapters: state.chapterResearch.chapters.map((chapter) =>
                chapter.id === chapterId ? { ...chapter, highLevelConsensus: consensus } : chapter
              ),
            },
          };
          
          // 同时将共识添加到历史记录
          setTimeout(() => {
            get().addChapterHistoricalConsensus(chapterId, consensus);
          }, 0);
          
          return updatedState;
        });
      },
      setChapterQueries: (chapterId, queries) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            chapters: state.chapterResearch.chapters.map((chapter) =>
              chapter.id === chapterId ? { ...chapter, finalQueries: queries } : chapter
            ),
          },
        }));
      },
      updateSearchProgress: (chapterId, progress) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            chapters: state.chapterResearch.chapters.map((chapter) =>
              chapter.id === chapterId ? { 
                ...chapter, 
                searchProgress: { 
                  totalQueries: 0,
                  completedQueries: 0,
                  summaryInProgress: false,
                  ...chapter.searchProgress, 
                  ...progress 
                } 
              } : chapter
            ),
          },
        }));
      },
      addAgentDiscussion: (discussion) => {
        console.log(`🔧 [Zustand Fix] 开始添加讨论，ID: ${discussion.id}`);
        
        set((state) => {
          const currentDiscussions = state.chapterResearch.discussions;
          
          // 检查重复，避免添加相同ID的讨论
          const exists = currentDiscussions.some(d => d.id === discussion.id);
          if (exists) {
            console.log(`⚠️ [重复跳过] 讨论${discussion.id}已存在，跳过添加`);
            return state; // 不更新状态
          }
          
          const newDiscussions = [...currentDiscussions, discussion];
          console.log(`🔧 [Zustand] 更新前: ${currentDiscussions.length}, 更新后: ${newDiscussions.length}`);
          
          return {
            chapterResearch: {
              ...state.chapterResearch,
              discussions: newDiscussions,
            },
          };
        });
        
        console.log(`✅ [Zustand] 成功添加讨论: ${discussion.id}`);
      },
      addChapterCollectedInfo: (info) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            collectedInfo: [...state.chapterResearch.collectedInfo, info],
          },
        }));
      },
      addChapterReport: (report) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            chapterReports: [...state.chapterResearch.chapterReports, report],
          },
        }));
      },
      resetChapterResearch: () => {
        set(() => ({
          chapterResearch: DEFAULT_CHAPTER_RESEARCH_STATE,
        }));
      },
      // 历史数据管理方法实现
      addChapterHistoricalConsensus: (chapterId, consensus) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            chapters: state.chapterResearch.chapters.map((chapter) =>
              chapter.id === chapterId
                ? {
                    ...chapter,
                    historicalConsensus: [
                      ...(chapter.historicalConsensus || []),
                      consensus
                    ]
                  }
                : chapter
            )
          }
        }));
      },
      addChapterHistoricalQueries: (chapterId, queries) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,  
            chapters: state.chapterResearch.chapters.map((chapter) =>
              chapter.id === chapterId
                ? {
                    ...chapter,
                    historicalQueries: [
                      ...(chapter.historicalQueries || []),
                      ...queries
                    ]
                  }
                : chapter
            )
          }
        }));
      },
      clearChapterHistoricalData: (chapterId) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            chapters: state.chapterResearch.chapters.map((chapter) =>
              chapter.id === chapterId
                ? {
                    ...chapter,
                    historicalConsensus: [],
                    historicalQueries: []
                  }
                : chapter
            )
          }
        }));
      },
      // 全局引用管理方法实现
      registerGlobalCitation: (source, chapterId, roundNumber) => {
        const state = get();
        const { url, title } = source;
        
        console.log(`🔢 [全局引用] 注册引用: ${url.substring(0, 50)}...`);
        console.log(`🔢 [全局引用] 当前计数器: ${state.chapterResearch.globalCitationCounter}`);
        console.log(`🔢 [全局引用] 现有引用数: ${state.chapterResearch.globalCitations.size}`);
        
        // 检查是否已存在
        if (state.chapterResearch.globalCitations.has(url)) {
          const existingIndex = state.chapterResearch.globalCitations.get(url)!.globalIndex;
          console.log(`🔢 [全局引用] 引用已存在，返回编号: [${existingIndex}]`);
          return existingIndex;
        }
        
        // 分配新编号
        const globalIndex = state.chapterResearch.globalCitationCounter + 1;
        const citation: GlobalCitation = {
          url,
          title: title || '未知标题', // 处理title可能为undefined的情况
          globalIndex,
          firstUsedInChapter: chapterId,
          firstUsedInRound: roundNumber,
        };
        
        console.log(`🔢 [全局引用] 分配新编号: [${globalIndex}] 给 ${url.substring(0, 50)}...`);
        
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            globalCitations: new Map(state.chapterResearch.globalCitations).set(url, citation),
            globalCitationCounter: globalIndex,
          }
        }));
        
        return globalIndex;
      },
      registerMultipleCitations: (sources, chapterId, roundNumber) => {
        const urlToIndexMap = new Map<string, number>();
        
        sources.forEach(source => {
          const globalIndex = get().registerGlobalCitation(source, chapterId, roundNumber);
          urlToIndexMap.set(source.url, globalIndex);
        });
        
        return urlToIndexMap;
      },
      getGlobalCitationByUrl: (url) => {
        const state = get();
        return state.chapterResearch.globalCitations.get(url);
      },
      getAllGlobalCitations: () => {
        const state = get();
        return Array.from(state.chapterResearch.globalCitations.values())
          .sort((a, b) => a.globalIndex - b.globalIndex);
      },
      // 设置第一章节预览报告
      setFirstChapterPreview: (preview) => {
        set((state) => ({
          chapterResearch: {
            ...state.chapterResearch,
            firstChapterPreview: preview,
          },
        }));
      },
    }),
    { 
      name: "research",
      // 优化持久化配置，避免大数组造成性能问题
      partialize: (state) => {
        // 对讨论记录去重并限制数量
        const uniqueDiscussions = Array.from(
          new Map(state.chapterResearch.discussions.map(d => [d.id, d])).values()
        ).slice(-30); // 最多保留最近30个讨论记录
        
        // 将 Map 转换为可序列化的数组格式
        const globalCitationsArray = Array.from(state.chapterResearch.globalCitations.entries());
        
        return {
          ...state,
          chapterResearch: {
            ...state.chapterResearch,
            discussions: uniqueDiscussions,
            // 保存为数组格式以支持序列化
            globalCitationsArray,
          },
        };
      },
      // 添加反序列化逻辑
      onRehydrateStorage: () => (state) => {
        if (state?.chapterResearch) {
          // 从数组格式恢复 Map
          const globalCitationsArray = (state.chapterResearch as any).globalCitationsArray || [];
          state.chapterResearch.globalCitations = new Map(globalCitationsArray);
          
          // 清理临时的数组字段
          delete (state.chapterResearch as any).globalCitationsArray;
          
          console.log(`🔄 [持久化恢复] 恢复了${globalCitationsArray.length}个全局引用编号`);
        }
      },
    }
  )
);
