// 章节式研究工具函数

import { ChapterInfo } from '@/types/chapter-research';

// 生成稳定的哈希值
function generateStableHash(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(36);
}

// 解析计划文本为章节列表
export function parsePlanToChapters(planText: string): ChapterInfo[] {
  console.log('🔍 [计划解析] 开始解析计划文本:', planText.substring(0, 200));
  
  try {
    // 首先尝试解析JSON格式（新的统一格式）
    if (planText.trim().startsWith('{')) {
      const planJson = JSON.parse(planText);
      if (planJson.sections && Array.isArray(planJson.sections)) {
      const chapters = planJson.sections.map((section: any, index: number): ChapterInfo => {
        const title = section.section_title || `第${index + 1}章`;
        const goal = section.summary || '研究目标待定';
        // 使用章节索引、标题和目标生成稳定的 ID
        const contentHash = generateStableHash(`${index}-${title}-${goal}`);
        const chapterId = `chapter-${index + 1}-${contentHash}`;
        
        return {
          id: chapterId,
          title: title,
          goal: goal,
          status: 'pending',
          currentRound: 0,
          currentPhase: 'high_level',
        };
      });
        
        console.log('✅ [计划解析] JSON格式解析成功，章节数:', chapters.length);
        return chapters;
      }
    }
    
    // 如果不是JSON格式，尝试解析文本格式（兼容旧格式）
    const chapters = parseTextPlanToChapters(planText);
    console.log('✅ [计划解析] 文本格式解析成功，章节数:', chapters.length);
    return chapters;
    
  } catch (error) {
    console.error('❌ [计划解析] JSON解析失败，尝试文本解析:', error);
    
    // JSON解析失败，回退到文本解析
    const chapters = parseTextPlanToChapters(planText);
    console.log('✅ [计划解析] 文本格式回退解析成功，章节数:', chapters.length);
    return chapters;
  }
}

// 解析文本格式的计划
function parseTextPlanToChapters(planText: string): ChapterInfo[] {
  const lines = planText.split('\n').filter(line => line.trim());
  const chapters: ChapterInfo[] = [];
  
  let currentChapter: Partial<ChapterInfo> | null = null;
  let chapterIndex = 0;
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // 检测章节标题（各种可能的格式）
    const chapterTitlePatterns = [
      /^#+\s*(.+)$/,           // ## 标题
      /^第?(\d+)[章节部分][:：]?\s*(.+)$/,  // 第1章：标题
      /^(\d+)[\.\)]\s*(.+)$/,  // 1. 标题 或 1) 标题
      /^[一二三四五六七八九十]+[、\.]\s*(.+)$/,  // 一、标题
    ];
    
    let isChapterTitle = false;
    let title = '';
    
    for (const pattern of chapterTitlePatterns) {
      const match = trimmedLine.match(pattern);
      if (match) {
        isChapterTitle = true;
        // 提取标题，处理不同的匹配组
        if (match.length > 2) {
          title = match[2]; // 有序号的情况
        } else {
          title = match[1]; // 简单标题的情况
        }
        break;
      }
    }
    
    if (isChapterTitle) {
      // 如果之前有章节，先保存它
      if (currentChapter && currentChapter.title) {
        chapters.push(finalizeChapter(currentChapter, chapterIndex));
        chapterIndex++;
      }
      
      // 开始新章节
      currentChapter = {
        id: '', // 将在 finalizeChapter 中生成稳定 ID
        title: title.trim(),
        goal: '',
        status: 'pending',
        currentRound: 0,
        currentPhase: 'high_level',
      };
    } else if (currentChapter && trimmedLine) {
      // 累积章节描述
      if (currentChapter.goal) {
        currentChapter.goal += ' ' + trimmedLine;
      } else {
        currentChapter.goal = trimmedLine;
      }
    }
  }
  
  // 保存最后一个章节
  if (currentChapter && currentChapter.title) {
    chapters.push(finalizeChapter(currentChapter, chapterIndex));
  }
  
  return chapters;
}

// 完善章节信息
function finalizeChapter(chapter: Partial<ChapterInfo>, index: number): ChapterInfo {
  const title = chapter.title || `第${index + 1}章`;
  const goal = chapter.goal || '研究目标待定';
  
  // 使用章节索引、标题和目标生成稳定的 ID
  const contentHash = generateStableHash(`${index}-${title}-${goal}`);
  const chapterId = `chapter-${index + 1}-${contentHash}`;
  
  return {
    id: chapterId,
    title: title,
    goal: goal,
    status: 'pending',
    currentRound: 0,
    currentPhase: 'high_level',
  };
}

// 验证章节解析结果
export function validateChapters(chapters: ChapterInfo[]): boolean {
  if (chapters.length === 0) {
    console.warn('⚠️ [章节验证] 没有解析到任何章节');
    return false;
  }
  
  if (chapters.length > 10) {
    console.warn('⚠️ [章节验证] 章节数量过多，可能影响性能:', chapters.length);
  }
  
  const invalidChapters = chapters.filter(chapter => 
    !chapter.id || !chapter.title || !chapter.goal
  );
  
  if (invalidChapters.length > 0) {
    console.warn('⚠️ [章节验证] 发现无效章节:', invalidChapters);
    return false;
  }
  
  console.log('✅ [章节验证] 章节验证通过，有效章节数:', chapters.length);
  return true;
}

// 获取章节的收集信息 - 返回实际搜索内容而非摘要
export function getChapterCollectedSummary(chapterId: string, collectedInfo: any[]): string {
  const chapterInfo = collectedInfo.filter(info => info.chapterId === chapterId);
  if (chapterInfo.length === 0) {
    return '暂无收集信息';
  }
  
  // 按轮次组织信息，提供实际内容
  const roundInfoMap = new Map<number, any>();
  chapterInfo.forEach(info => {
    if (!roundInfoMap.has(info.roundNumber)) {
      roundInfoMap.set(info.roundNumber, info);
    }
  });
  
  // 配置：控制内容长度
  const MAX_CHARS_PER_RESULT = 1500;  // 每个搜索结果最多1500字符
  const MAX_TOTAL_CHARS = 8000;       // 总共最多8000字符
  let totalChars = 0;
  
  const formattedInfo = Array.from(roundInfoMap.values())
    .sort((a, b) => a.roundNumber - b.roundNumber)
    .map(info => {
      // 如果已经超过总长度限制，返回简短摘要
      if (totalChars >= MAX_TOTAL_CHARS) {
        return `第${info.roundNumber}轮: [内容过长已省略，共${info.searchResults?.length || 0}个查询]`;
      }
      
      const roundHeader = `【第${info.roundNumber}轮收集的信息】`;
      
      // 提取每个查询的实际结果内容
      const searchContents = info.searchResults?.map((result: any, index: number) => {
        // 检查是否已超过总长度
        if (totalChars >= MAX_TOTAL_CHARS) {
          return '[后续内容已省略]';
        }
        
        const query = result.query || '未知查询';
        const goal = result.researchGoal || '未知目标';
        let content = result.results || '无内容';
        
        // 限制单个搜索结果的长度
        if (content.length > MAX_CHARS_PER_RESULT) {
          content = content.substring(0, MAX_CHARS_PER_RESULT) + '...[内容已截断]';
        }
        
        const formattedResult = `\n查询${index + 1}: "${query}"\n目标: ${goal}\n内容:\n${content}`;
        totalChars += formattedResult.length;
        
        return formattedResult;
      }).filter(Boolean).join('\n\n---\n') || '无搜索结果';
      
      return `${roundHeader}\n${searchContents}`;
    })
    .join('\n\n════════════════════════════════\n\n');
  
  // 如果内容被截断，添加提示
  if (totalChars >= MAX_TOTAL_CHARS) {
    return formattedInfo + '\n\n[注意：由于内容过长，部分信息已被省略]';
  }
  
  return formattedInfo || '暂无信息内容';
}

// 生成章节研究状态报告
export function generateChapterStatusReport(chapters: ChapterInfo[]): string {
  const statusCounts = {
    pending: 0,
    discussing: 0,
    searching: 0,
    writing: 0,
    completed: 0,
  };
  
  chapters.forEach(chapter => {
    statusCounts[chapter.status]++;
  });
  
  return `
章节研究进度报告：
- 总章节数：${chapters.length}
- 待开始：${statusCounts.pending}
- 讨论中：${statusCounts.discussing}
- 搜索中：${statusCounts.searching}
- 写作中：${statusCounts.writing}
- 已完成：${statusCounts.completed}
  `.trim();
}