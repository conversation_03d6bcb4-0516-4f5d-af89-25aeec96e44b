我需要在Deep Research项目中实现一个基于多轮对抗机制的研究计划优化功能。请注意：这是纯讨论阶段，不要修改任何代码。

**功能背景：**
- 当前Deep Research已实现：用户输入研究主题 → LLM澄清追问 → 用户回答 → 生成研究计划plan → 基于plan的自动化信息收集
- 现在需要在"生成研究计划plan"这一步骤中在原本的基础上实现多轮对抗优化机制，也就是原流程也可以继续用

**核心需求：**
1. **双角色对抗机制：**
   - 构建者角色：根据完整query（包含用户研究主题、LLM澄清追问、用户澄清回答）和批判者反馈，生成/修改/完善研究计划plan
   - 批判者角色：审查构建者的plan，确保plan完整性、相关性、无重复性，提供具体改进建议

2. **轮数控制参数：**
   - 用户可设定固定对抗轮数（如3轮、5轮）
   - 或选择自动模式：由批判者判断plan质量决定何时停止对抗

3. **技术一致性要求：**
   - 最大限度复用现有流程和代码架构
   - 保持与原有技术栈、数据结构、代码风格的一致性
   - 无缝集成到现有的plan生成流程中

**参考资料：**
- `docs/plan对抗机制.md` 包含初步框架设计思路

**讨论重点：**
请基于现有Deep Research代码架构，思考并讨论：
1. 对抗机制的具体实现方案（prompt设计、数据流转、状态管理）
2. 如何与现有plan生成流程集成
3. 用户界面交互设计（轮数设置、进度显示、结果展示）
4. 技术实现细节和潜在挑战

请先分析现有代码结构，然后提出详细的技术方案供讨论