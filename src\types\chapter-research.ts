// 章节式研究相关类型定义

// 全局引用管理
export interface GlobalCitation {
  url: string;
  title: string;
  globalIndex: number;
  firstUsedInChapter?: string;
  firstUsedInRound?: number;
}

// 智能体类型
export type AgentId = 'Alpha' | 'Beta';

// 动作类型
export type AgentActionName = 
  | 'HIGH_LEVEL_PROPOSE'      // 高层级提议
  | 'HIGH_LEVEL_AGREE'        // 高层级同意
  | 'QUERY_PROPOSE'           // 查询提议
  | 'EXECUTE_QUERIES';        // 执行查询

// 智能体动作负载
export interface AgentActionPayload {
  proposal?: string;           // 提议内容
  agreement?: string;          // 同意内容
  queries?: Array<{           // 查询列表
    query: string;
    researchGoal: string;
  }>;
  rationale?: string;         // 理由说明
}

// 智能体对话记录
export interface AgentDiscussion {
  id: string;
  chapterId: string;
  roundNumber: number;        // 轮数（1,2,3...）
  turnNumber: number;         // 回合数（每轮中Alpha和Beta的对话次数）
  agentId: AgentId;
  actionName: AgentActionName;
  payload: AgentActionPayload;
  thought: string;            // 思考过程
  timestamp: number;
}

// 搜索进度状态
export interface SearchProgress {
  totalQueries: number;       // 总查询数
  completedQueries: number;   // 已完成查询数  
  currentQuery?: string;      // 当前正在处理的查询
  summaryInProgress: boolean; // 是否正在总结
}

// 章节信息
export interface ChapterInfo {
  id: string;
  title: string;              // 章节标题
  goal: string;               // 章节目标（从summary解析）
  status: 'pending' | 'discussing' | 'searching' | 'writing' | 'completed';
  currentRound: number;       // 当前对话轮数
  currentPhase: 'high_level' | 'query' | 'searching'; // 当前阶段
  highLevelConsensus?: string; // 高层级共识结果
  finalQueries?: Array<{      // 最终确定的查询
    query: string;
    researchGoal: string;
  }>;
  searchProgress?: SearchProgress; // 搜索进度状态
  // 新增历史数据字段
  historicalConsensus?: string[]; // 历史高层级共识累积
  historicalQueries?: Array<{      // 历史查询累积  
    query: string;
    researchGoal: string;
    fromRound: number;
  }>;
}

// 章节收集的信息
export interface ChapterCollectedInfo {
  chapterId: string;
  roundNumber: number;
  searchResults: Array<{
    query: string;
    researchGoal: string;
    results: string;          // 搜索结果内容
    sources: Source[];        // 来源信息
    images: ImageSource[];    // 新增：图片来源
  }>;
  summary: string;            // 本轮收集信息的总结
  // 新增：汇总的图片信息，用于后续报告生成
  aggregatedImages: ImageSource[];
}

// 章节报告
export interface ChapterReport {
  chapterId: string;
  title: string;
  content: string;           // 章节报告内容
  sources: Source[];         // 引用来源
  images: ImageSource[];     // 新增：报告中使用的图片
  generatedAt: number;       // 生成时间
}

// 章节式研究配置
export interface ChapterResearchConfig {
  maxRounds: number;         // 一个章节最大研究轮数（每轮=高层级共识+查询执行）
  highLevelTurns: number;    // 每轮高层级阶段最大对话回合数（Alpha/Beta交替对话次数）
  queryTurns: number;        // 每轮查询阶段最大对话回合数（Alpha/Beta交替对话次数）
  autoExecuteQueries: boolean; // 是否自动执行查询
  enableInfoSummarization: boolean; // 是否启用信息总结（用AI总结搜索结果）
  // 新增配置选项
  includeCollectedInfo: boolean; // 是否在prompt中包含已收集信息
  enableImages: boolean; // 是否在报告中包含图片
}

// 章节式研究状态
export interface ChapterResearchState {
  isActive: boolean;         // 是否启用章节式研究
  isRunning: boolean;        // 是否正在运行
  showConfig: boolean;       // 是否显示配置界面
  config: ChapterResearchConfig;
  
  // 数据
  chapters: ChapterInfo[];
  discussions: AgentDiscussion[];
  collectedInfo: ChapterCollectedInfo[];
  chapterReports: ChapterReport[];
  
  // 全局引用管理
  globalCitations: Map<string, GlobalCitation>; // url -> citation info
  globalCitationCounter: number; // 全局编号计数器
  
  // 运行时状态
  currentChapterId?: string;
  userQuery: string;         // 用户原始查询，作为背景输入
  firstChapterPreview?: string; // 新增：第一章节预览报告（无参考来源版本）
}

// 默认配置
export const DEFAULT_CHAPTER_RESEARCH_CONFIG: ChapterResearchConfig = {
  maxRounds: 2,              // 每个章节最多3轮研究（高层级共识+查询执行）
  highLevelTurns: 6,         // 高层级阶段最多4回合对话（达到阈值强制进入查询阶段）
  queryTurns: 6,             // 查询阶段最多4回合对话（达到阈值强制执行查询）
  autoExecuteQueries: true,  // 自动执行查询
  enableInfoSummarization: true, // 默认使用llm总结
  includeCollectedInfo: false, // 默认不启用历史信息功能
  enableImages: false,        // 默认不启用图片功能
};

// 默认状态
export const DEFAULT_CHAPTER_RESEARCH_STATE: ChapterResearchState = {
  isActive: false,
  isRunning: false,
  showConfig: false,
  config: DEFAULT_CHAPTER_RESEARCH_CONFIG,
  
  chapters: [],
  discussions: [],
  collectedInfo: [],
  chapterReports: [],
  
  globalCitations: new Map(),
  globalCitationCounter: 0,
  
  userQuery: '',
};

// 章节状态枚举
export const ChapterStatus = {
  PENDING: 'pending' as const,
  DISCUSSING: 'discussing' as const,
  SEARCHING: 'searching' as const,
  WRITING: 'writing' as const,
  COMPLETED: 'completed' as const,
};

// 研究阶段枚举
export const ResearchPhase = {
  HIGH_LEVEL: 'high_level' as const,
  QUERY: 'query' as const,
  SEARCHING: 'searching' as const,
};