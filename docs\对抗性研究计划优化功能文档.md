# 对抗性研究计划优化功能文档

## 1. 功能概述

本项目在原有的研究计划（plan）生成流程基础上，新增了一套可选的、基于多轮对抗机制的“高级计划优化”功能。该功能通过模拟“架构师”（Architect）与“批判者”（Critic）两个AI角色的多轮对话，对研究大纲进行反复迭代和完善，旨在产出结构更严谨、内容更全面、逻辑更清晰的高质量研究计划。

该功能被设计为一个嵌入式的、非阻塞的UI组件，用户在确认核心研究问题后，可以自由选择执行标准流程或启动高级优化流程。优化过程和历史记录会被完整保留，供用户随时复盘。

## 2. 技术架构与核心实现

### 2.1 状态管理 (`src/store/task.ts`)

我们选择在核心的 `useTaskStore` (Zustand) 中扩展与对抗流程相关的状态，以实现组件间通信和状态持久化。

- **新增状态对象**: 在 `TaskStore` 接口中添加了可选的 `adversarialPlan` 字段，其类型为 `AdversarialPlanState`。
  ```typescript
  export interface AdversarialPlanState {
    rounds: AdversarialRound[];
    currentRound: number;
    maxRounds: number;
    mode: 'fixed' | 'auto';
    isActive: boolean;
    isRunning: boolean;
    currentPersona: '架构师' | '批判者';
    conversationText: string;
  }
  ```
- **相关Action**: 添加了初始化 (`initAdversarialPlan`)、运行 (`setAdversarialPlanRunning`)、增加轮次 (`addAdversarialRound`)、结束 (`finishAdversarialPlan`) 和重置 (`resetAdversarialPlan`) 等一系列Action来管理 `adversarialPlan` 对象的生命周期。
- **生命周期管理**: 为了解决状态污染问题，在 `Feedback.tsx` 的 `handleSubmit` 函数（即新任务的起点）中会首先调用 `resetAdversarialPlan()`，确保每次任务都是从一个干净的状态开始。

### 2.2 核心 Hook (`src/hooks/useDeepResearch.ts`)

所有与AI交互的核心逻辑都被封装在 `useDeepResearch` 这个Hook中，以保持UI组件的纯粹性。

- **新增函数**: 添加了 `runAdversarialPlanOptimization` 异步函数。此函数负责驱动整个对抗流程，包括：
    1.  循环调用语言模型（`streamText`）。
    2.  根据当前角色（架构师/批判者）和对话历史，动态组装Prompt。
    3.  解析模型返回的JSON结果。
    4.  调用Zustand的Action来更新中心状态（如添加轮次记录、更新对话文本等）。
    5.  处理结束条件（批判者决定完成或达到最大轮数）。
    6.  最终调用 `finishAdversarialPlan` 来更新全局的 `reportPlan`。

### 2.3 Prompt 设计 (`src/utils/deep-research/prompts.ts`)

功能的智能核心在于精巧的Prompt工程。

- **新增 `getReportPlanPrompt` 函数**: 这是一个动态Prompt生成函数，它会根据传入的参数（`nextPersona`, `query`, `conversationText`, `isFirstTurn`）来构建一个高度结构化、规则明确的指令。
- **角色与规则**: Prompt中清晰地定义了架构师和批判者的职责、行动 (`action`)、商议循环规则、以及各自必须遵守的准则，引导模型进行高质量的结构化对抗。
- **JSON Schema**: 通过在Prompt中声明返回格式，并结合 `planTurnSchema`，确保了模型输出的稳定性和可解析性。

### 2.4 前端组件

我们采用“容器”与“展示”分离的思路，实现了模块化和可复用的UI。

- **`Feedback.tsx` (容器/触发器)**
    - 管理 `AdversarialPlanOptimizer` 组件的**可见性**（通过 `showAdversarialOptimizer` state）。
    - 在用户确认核心问题后，提供“高级计划优化”按钮作为**功能入口**。
    - 点击按钮后，在主界面原地渲染优化器组件。

- **`AdversarialPlanOptimizer.tsx` (核心UI)**
    - **嵌入式设计**: 这是一个完全独立的、自包含的组件，移除了所有弹窗（Dialog）逻辑。
    - **可展开/收缩**: 组件的根 `<Card>` 标题栏是可点击的，通过 `isExpanded` state 控制主体内容的显示和隐藏，实现了非阻塞式交互。
    - **动态状态栏**: 在收缩状态下，标题栏右侧会根据流程状态（待配置、运行中、已完成）显示一个动态的徽章（Badge），提供实时反馈。
    - **扁平化UI**: 内部移除了“卡片套卡片”的冗余结构，使用 `<Separator>` 进行区域分割，实现了视觉上的和谐统一。
    - **配置与启动分离**: 组件内部管理配置阶段和执行阶段。用户必须在组件内部点击“开始优化”按钮才能启动流程，保证了操作的明确性。

## 3. 用户交互流程

1.  用户在“2. 提出您的想法”区域完成对系统提问的反馈。
2.  点击“**确认反馈并继续**”按钮。此时，系统会自动清除上一次可能残留的优化历史，并锁定核心研究问题。
3.  界面出现两个并行的选项：“**撰写报告方案**”和“**高级计划优化**”。
4.  用户点击“**高级计划优化**”。
5.  `AdversarialPlanOptimizer` 组件出现在主界面，默认显示**配置面板**（可设置轮数、模式）。
6.  用户完成配置，点击组件内的“**开始优化**”按钮。
7.  配置面板消失，转为**进度显示**和**对话历史**区域。流程开始运行。
8.  用户可以随时点击组件标题栏来**展开/收缩**内容区域，方便查看或节省空间。
9.  优化流程结束后，组件会停留在**完成状态**，所有历史记录被保留，供随时复盘。
10. 最终生成的 `plan` 会自动填充到“2.2 研究报告方案”区域，后续的研究流程可以无缝衔接。
