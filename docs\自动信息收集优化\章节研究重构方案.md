# 章节研究重构方案

## 概述

基于状态机设计，将复杂的AI判断逻辑拆分为5个专用AI，每个AI职责明确，避免复杂的状态判断和无限循环问题。

## 核心设计原则

### 1. 代码控制流程，AI专注内容
- 代码负责状态转换、循环控制、流程管理
- AI只负责在明确约束下生成内容
- 通过超参数控制防止无限循环

### 2. 两阶段思考准则
- **阶段一：战略差距评估** - 宏观评估知识覆盖广度，输出薄弱知识点列表
- **阶段二：战术细节规划** - 在确定主题内进行精细化查询规划

### 3. 职责边界清晰
- 高层级阶段：确定研究方向（薄弱知识点）
- 查询阶段：制定具体执行方案
- 严格的输入输出格式，避免信息冗余

## 五个专用AI设计

### 1. 开场AI
- **触发条件**: 章节开始，第一次发言
- **输入**: 
  - 章节目标
  - 原始收集信息（所有历史查询结果）
- **输出**: 
  - 思考（第一阶段：薄弱知识点评估）
  - 提议（薄弱知识点列表）
  - 动作（HIGH_LEVEL_PROPOSE）

### 2. 高层级响应AI
- **触发条件**: 对方执行了HIGH_LEVEL_PROPOSE
- **输入**: 
  - 章节目标
  - 当前轮次讨论历史（包含对方HIGH_LEVEL_PROPOSE）
  - 原始收集信息（所有历史查询结果）
- **输出**: 
  - 思考（第一阶段：评估对方薄弱知识点）
  - 提议（同意或新的薄弱知识点列表）
  - 动作（HIGH_LEVEL_AGREE | HIGH_LEVEL_PROPOSE）

### 3. 查询开始AI
- **触发条件**: 对方执行了HIGH_LEVEL_AGREE
- **输入**: 
  - 章节目标
  - 高层级共识结果（薄弱知识点列表）
  - 原始收集信息（所有历史查询结果）
- **输出**: 
  - 思考（第二阶段：具体查询规划）
  - 提议（具体查询列表）
  - 动作（QUERY_PROPOSE）

### 4. 查询响应AI
- **触发条件**: 对方执行了QUERY_PROPOSE
- **输入**: 
  - 章节目标
  - 高层级共识结果（薄弱知识点列表）
  - 当前轮次查询讨论历史（包含对方QUERY_PROPOSE）
  - 原始收集信息（所有历史查询结果）
- **输出**: 
  - 思考（第二阶段：评估查询方案）
  - 提议（同意或新查询列表）
  - 动作（EXECUTE_QUERIES | QUERY_PROPOSE）

### 5. 章节结束判断AI
- **触发条件**: 达到轮数阈值后
- **输入**: 
  - 章节目标
  - 所有轮次讨论历史
  - 原始收集信息（所有历史查询结果）
- **输出**: 
  - 判断（是否结束章节）
  - 理由

## 状态机流程控制

```
while(本章节讨论轮数 < 阈值轮数) {
    if(开场) {
        执行开场AI → HIGH_LEVEL_PROPOSE
    }
    if(对方动作 == HIGH_LEVEL_PROPOSE) {
        执行高层级响应AI → HIGH_LEVEL_AGREE | HIGH_LEVEL_PROPOSE
        if(循环次数 > 高层级最大循环次数) {
            强制选择HIGH_LEVEL_AGREE
        }
    }
    if(对方动作 == HIGH_LEVEL_AGREE) {
        执行查询开始AI → QUERY_PROPOSE
    }
    if(对方动作 == QUERY_PROPOSE) {
        执行查询响应AI → QUERY_PROPOSE | EXECUTE_QUERIES
        if(循环次数 > 查询最大循环次数) {
            强制选择EXECUTE_QUERIES
        }
    }
    if(对方动作 == EXECUTE_QUERIES) {
        执行查询，轮数++，重新开始
    }
}

if(本章节讨论轮数 < 最大轮数) {
    执行章节结束判断AI
} else {
    强制结束本章节
}
```

## 超参数配置

```typescript
interface ChapterResearchConfig {
  // 循环控制参数
  maxHighLevelLoops: number;        // 高层级讨论最大循环次数
  maxQueryLoops: number;            // 查询讨论最大循环次数
  roundThreshold: number;           // 章节轮数阈值
  maxRounds: number;                // 章节最大轮数
  
  // 用户可配置
  userConfigurable: boolean;
}
```

## 数据结构设计

```typescript
interface ChapterRoundData {
  roundNumber: number;
  
  // 高层级阶段
  highLevelDiscussions: Discussion[];
  highLevelConsensus?: string;  // 达成的薄弱知识点列表
  
  // 查询阶段  
  queryDiscussions: Discussion[];
  queryResults?: QueryResult[];
  
  // 状态
  phase: 'HIGH_LEVEL' | 'QUERY' | 'COMPLETED';
}

interface ChapterState {
  id: string;
  goal: string;
  rounds: ChapterRoundData[];
  allCollectedInfo: QueryResult[];  // 所有历史查询结果
  status: 'ACTIVE' | 'COMPLETED';
}
```

## 前端呈现方式

### 1. 对话框结构
- 保持现有的ChapterDebateView组件
- 按轮次分组显示讨论
- 区分高层级阶段和查询阶段

### 2. 显示内容
- **思考过程**: 显示AI的两阶段思考
- **提议内容**: 根据阶段显示薄弱知识点或查询列表
- **动作类型**: 用图标和颜色区分不同动作
- **阶段标识**: 清晰标识当前处于哪个阶段

### 3. 进度展示
- 轮次进度条
- 当前阶段指示器
- 循环次数提示（防止用户困惑）

## 实现优势

1. **逻辑清晰**: 每个AI职责单一，不会混淆
2. **防死循环**: 代码硬性控制，用户可配置参数
3. **易于调试**: 状态转换在代码中明确可见
4. **职责分离**: AI专注内容，代码管理流程
5. **用户可控**: 关键参数可由用户调整

## 实现状态

### ✅ 已完成
- [x] 5个专用AI的Prompt模板设计
- [x] 状态机流程控制逻辑
- [x] 超参数配置系统
- [x] 循环控制和强制推进机制
- [x] 章节结束判断功能
- [x] 前端对话框显示组件
- [x] 数据结构扩展和状态管理

### 🔧 核心特性
- **智能循环控制**: 自动检测和防止无限循环
- **强制推进机制**: 达到最大次数时自动推进流程
- **章节结束判断**: AI智能判断章节是否完成
- **轮数限制**: 防止章节讨论过长
- **实时状态跟踪**: 完整的阶段和循环计数

### 📊 配置参数
```typescript
{
  maxHighLevelLoops: 3,    // 高层级最大循环次数
  maxQueryLoops: 3,        // 查询最大循环次数
  roundThreshold: 2,       // 开始判断结束的轮数
  maxRounds: 5            // 强制结束的最大轮数
}
```

**重构完成！章节研究现在具备了完整的状态机控制、循环防护和智能结束判断能力。**
