import crypto from 'crypto';

/**
 * 生成URL的临时引用标识符
 * @param url 源URL
 * @returns 格式为 REF_xxxxxxxx 的临时标识符
 */
export function generateRefId(url: string): string {
  // 使用URL的MD5 hash前8位作为标识符
  const hash = crypto.createHash('md5').update(url).digest('hex').substring(0, 8);
  return `REF_${hash}`;
}

/**
 * 引用信息接口
 */
export interface ReferenceInfo {
  refId: string;
  url: string;
  title: string;
  finalIndex?: number;
}

/**
 * 从文本中提取所有临时引用标识符
 * @param text 要扫描的文本
 * @returns 提取到的引用标识符数组
 */
export function extractRefIds(text: string): string[] {
  const refPattern = /\[REF_[a-f0-9]{8}\]/g;
  const matches = text.match(refPattern) || [];
  return Array.from(new Set(matches.map(match => match.slice(1, -1)))); // 去除方括号并去重
}

/**
 * 统一处理引用编号
 * @param texts 包含临时引用的文本数组
 * @param sourceMap URL到ReferenceInfo的映射
 * @returns 处理后的文本数组和最终的引用列表
 */
export function unifyReferences(
  texts: string[], 
  sourceMap: Map<string, ReferenceInfo>
): {
  processedTexts: string[];
  referencesList: ReferenceInfo[];
} {
  // 1. 从所有文本中提取引用标识符
  const allRefIds = new Set<string>();
  texts.forEach(text => {
    const refIds = extractRefIds(text);
    refIds.forEach(refId => allRefIds.add(refId));
  });

  // 2. 建立映射表：临时标识符 -> 最终编号
  const refMapping = new Map<string, number>();
  const referencesList: ReferenceInfo[] = [];
  
  Array.from(allRefIds).forEach((refId, index) => {
    const finalIndex = index + 1;
    refMapping.set(refId, finalIndex);
    
    // 从sourceMap中找到对应的引用信息
    const referenceInfo = Array.from(sourceMap.values()).find(ref => ref.refId === refId);
    if (referenceInfo) {
      referencesList.push({
        ...referenceInfo,
        finalIndex
      });
    }
  });

  // 3. 批量替换所有文本中的临时标识符
  const processedTexts = texts.map(text => {
    let processedText = text;
    refMapping.forEach((finalIndex, refId) => {
      const pattern = new RegExp(`\\[${refId}\\]`, 'g');
      processedText = processedText.replace(pattern, `[${finalIndex}]`);
    });
    return processedText;
  });

  return { processedTexts, referencesList };
}

/**
 * 生成最终的参考文献列表
 * @param referencesList 引用信息列表
 * @returns 格式化的参考文献字符串
 */
export function generateReferencesList(referencesList: ReferenceInfo[]): string {
  if (referencesList.length === 0) {
    return '';
  }

  const sortedReferences = referencesList.sort((a, b) => (a.finalIndex || 0) - (b.finalIndex || 0));
  
  return sortedReferences
    .map(ref => `[${ref.finalIndex}] ${ref.title} - ${ref.url}`)
    .join('\n');
}