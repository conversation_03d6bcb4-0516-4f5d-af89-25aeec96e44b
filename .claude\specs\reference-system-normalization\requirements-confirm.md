# 引用系统标准化功能 - 需求确认文档

## 原始需求
实现上述想法：基于CSS渲染的正确引用链接，重新编号并标准化引用系统，解决引用编号跳跃和不匹配问题。

## 需求澄清过程

### 第一轮澄清问题
1. **功能触发时机**：何时执行标准化处理？是否需要手动触发？
2. **处理范围**：处理哪种研究模式？如何识别？
3. **用户体验**：是否需要进度提示、预览、回退功能？
4. **技术实现细节**：基于什么数据源？如何处理冲突？是否需要备份？
5. **边界情况处理**：无引用、无效引用、URL无法访问等情况？

### 用户回答
1. 最终报告生成后自动执行，不需要手动触发
2. 只处理章节研究的最终报告，通过检测识别
3. 不需要可视化进度，不考虑回退机制
4. 基于解析渲染后的HTML（鼠标悬停链接），不考虑冲突和备份
5. 边界情况暂不考虑复杂处理

## 最终确认需求

### 功能目标
在章节研究生成最终报告后，自动执行引用系统标准化，确保引用编号连续且与链接完全匹配

### 核心功能点
1. **自动触发**：章节研究最终报告生成后立即执行
2. **HTML解析**：从渲染后的HTML中提取正确的引用链接信息
3. **重新编号**：生成从1开始的连续编号，解决跳跃问题
4. **去重处理**：相同URL使用相同编号，确保一致性
5. **内容替换**：将正文中的旧编号替换为新编号
6. **引用重建**：删除原有引用部分，添加标准化的引用列表

### 技术实现要求
- 修改章节研究的 `generateFinalReport` 函数
- 实现HTML解析逻辑，提取reference类链接的href和文本
- 实现编号映射和内容替换算法
- 确保只处理章节研究模式，不影响传统研究流程

### 处理流程
1. 检测是否为章节研究模式
2. 解析最终报告的HTML渲染结果
3. 提取所有reference类链接的URL和原编号
4. 按出现顺序重新分配连续编号（去重）
5. 替换正文中的引用编号
6. 删除原有引用部分，添加新的标准化引用列表
7. 更新最终报告内容

### 质量标准
- 确保引用编号从1开始连续无跳跃
- 保证正文引用与引用列表完全匹配
- 相同URL在正文中多次出现时使用相同编号
- 不影响非章节研究的其他功能

## 需求质量评分：92/100

**功能清晰度 (27/30分)：**
- ✅ 明确了自动触发时机和处理范围
- ✅ 明确了基于HTML解析的技术方案
- ✅ 明确了重新编号和去重的具体需求

**技术特异性 (23/25分)：**
- ✅ 明确修改generateFinalReport函数
- ✅ 明确基于HTML解析的实现方案
- ✅ 明确只处理章节研究模式

**实现完整性 (22/25分)：**
- ✅ 明确了完整的处理流程
- ✅ 明确了核心算法需求
- ✅ 明确了不需要复杂的错误处理

**业务上下文 (20/20分)：**
- ✅ 解决引用不匹配问题，提升用户体验
- ✅ 确保下载的markdown文件引用完整可用
- ✅ 不影响现有功能的稳定性

## 确认时间
2025-08-13

## 状态
需求确认完成，等待用户批准进入实现阶段