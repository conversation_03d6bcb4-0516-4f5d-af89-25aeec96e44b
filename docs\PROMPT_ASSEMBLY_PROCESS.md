# Prompt组装流程详解

## 概述

Deep Research 项目采用分层组装式Prompt架构，通过将不同功能的prompt片段组合，生成最终的完整prompt。这种设计使得系统具有高度的灵活性和可扩展性。

## 组装架构

### 1. 核心数据层 (Core Data Layer)
**作用**: 提供研究过程中收集的所有原始数据
**位置**: Prompt开头
**格式**:
```xml
<RESEARCH_DATA>
<LEARNINGS>
{learnings}
</LEARNINGS>

<SOURCES>
{sources}
</SOURCES>

<IMAGES>
{images}
</IMAGES>
</RESEARCH_DATA>
```

**数据内容**:
- `learnings`: 所有搜索任务的学习内容，用换行符连接
- `sources`: JSON格式的来源数据，包含title和url
- `images`: JSON格式的图片数据，包含url和description

### 2. 计划参考层 (Plan Reference Layer)
**作用**: 控制原始研究计划对最终报告的影响程度
**位置**: 核心数据层之后
**权重类型**:

#### Strong权重 (严格遵循)
```xml
<REPORT_STRUCTURE>
Strictly follow this confirmed report plan structure:
{plan}
</REPORT_STRUCTURE>
```

#### Weak权重 (参考但优先新格式)
```xml
<STRUCTURE_REFERENCE>
You may reference this original plan if helpful, but prioritize the format requirements below:
{plan}
</STRUCTURE_REFERENCE>
```

#### None权重 (完全忽略)
```
(不包含任何计划参考内容)
```

### 3. 用户需求层 (User Requirements Layer)
**作用**: 包含用户的特殊写作要求
**位置**: 计划参考层之后
**格式**:
```xml
<USER_REQUIREMENTS>
{requirement}
</USER_REQUIREMENTS>
```

**条件**: 仅当用户输入了写作要求时才包含

### 4. 格式指导层 (Format Instruction Layer)
**作用**: 定义特定报告格式的详细要求
**位置**: 用户需求层之后
**格式**:
```xml
<FORMAT_INSTRUCTIONS>
{formatSpecificInstructions}
</FORMAT_INSTRUCTIONS>
```

### 5. 最终指令层 (Final Instruction Layer)
**作用**: 确保AI只返回报告内容
**位置**: Prompt结尾
**格式**:
```
**Respond only the final report content, and no additional text before or after.**
```

## 组装流程

### 步骤1: 数据准备
```typescript
// 从任务状态中提取数据
const learnings = tasks.map(item => item.learning).join('\n\n');
const sources = enableReferences ? 
  sources.map(item => pick(item, ["title", "url"])) : [];
const images = enableCitationImage ? images : [];
```

### 步骤2: 权重确定
```typescript
function getDefaultPlanWeight(reportType: ReportType): PlanWeight {
  const weights: Record<ReportType, PlanWeight> = {
    standard: 'strong',           // 标准格式严格遵循计划
    'collateral-analysis': 'weak', // 担保品分析参考计划
    custom: 'weak'                // 自定义格式参考计划
  };
  return weights[reportType];
}
```

### 步骤3: 格式指导获取
```typescript
// 预定义格式
let formatInstruction = formatInstructions[reportType];

// 自定义格式
if (reportType === 'custom' && customFormat) {
  formatInstruction = customFormat.prompt;
}
```

### 步骤4: 片段组装
```typescript
const sections = [
  // 1. 核心数据层
  coreDataSection
    .replace('{learnings}', data.learnings)
    .replace('{sources}', JSON.stringify(data.sources, null, 2))
    .replace('{images}', JSON.stringify(data.images, null, 2)),
  
  // 2. 计划参考层
  planSections[weight]?.replace('{plan}', data.plan) || '',
  
  // 3. 用户需求层
  data.requirement ? 
    `<USER_REQUIREMENTS>\n${data.requirement}\n</USER_REQUIREMENTS>` : '',
  
  // 4. 格式指导层
  `<FORMAT_INSTRUCTIONS>\n${formatInstruction}\n</FORMAT_INSTRUCTIONS>`,
  
  // 5. 最终指令层
  `**Respond only the final report content, and no additional text before or after.**`
];
```

### 步骤5: 最终组装
```typescript
return sections.filter(Boolean).join('\n\n');
```

## 完整示例

### 输入数据
```typescript
const data = {
  learnings: "市场规模增长15%。主要竞争对手包括A公司、B公司。",
  sources: [
    {"title": "市场报告2023", "url": "https://example.com/report"},
    {"title": "竞争分析", "url": "https://example.com/analysis"}
  ],
  images: [
    {"url": "https://example.com/chart.png", "description": "市场增长图表"}
  ],
  plan: "1. 市场概况\n2. 竞争分析\n3. 趋势预测",
  requirement: "重点关注新兴市场机会"
};
```

### 组装后的完整Prompt
```
<RESEARCH_DATA>
<LEARNINGS>
市场规模增长15%。主要竞争对手包括A公司、B公司。
</LEARNINGS>

<SOURCES>
[
  {
    "title": "市场报告2023",
    "url": "https://example.com/report"
  },
  {
    "title": "竞争分析",
    "url": "https://example.com/analysis"
  }
]
</SOURCES>

<IMAGES>
[
  {
    "url": "https://example.com/chart.png",
    "description": "市场增长图表"
  }
]
</IMAGES>
</RESEARCH_DATA>

<STRUCTURE_REFERENCE>
You may reference this original plan if helpful, but prioritize the format requirements below:
1. 市场概况
2. 竞争分析
3. 趋势预测
</STRUCTURE_REFERENCE>

<USER_REQUIREMENTS>
重点关注新兴市场机会
</USER_REQUIREMENTS>

<FORMAT_INSTRUCTIONS>
Write a comprehensive market analysis report with the following structure:

## **Executive Summary**
- Brief overview of key market findings
- Highlight of major trends and opportunities

## **Market Overview**
- Current market size and growth rate
- Key players and market share analysis

## **Competitive Landscape**
- Detailed competitor analysis
- Strengths and weaknesses assessment

Use data-driven insights throughout the report.
</FORMAT_INSTRUCTIONS>

**Respond only the final report content, and no additional text before or after.**
```

## 不同格式的组装差异

### 标准格式 (Standard)
- **Plan权重**: Strong
- **特点**: 严格遵循原始研究计划结构
- **适用**: 需要保持原有研究框架的报告

### 担保品分析格式 (Collateral Analysis)
- **Plan权重**: Weak
- **特点**: 参考原计划但优先专业格式要求
- **适用**: 金融机构的专业分析报告

### 自定义格式 (Custom)
- **Plan权重**: 可配置
- **特点**: 完全由用户定义的格式要求
- **适用**: 特殊需求和个性化报告

## 扩展机制

### 添加新格式
1. 在 `formatInstructions` 对象中添加新的格式指导
2. 定义该格式的默认Plan权重
3. 添加相应的类型定义和配置

### 动态权重调整
```typescript
// 可以根据内容特征动态调整权重
function getDynamicPlanWeight(reportType: string, contentFeatures: any): PlanWeight {
  if (contentFeatures.hasStructuredData) {
    return 'strong';
  }
  if (contentFeatures.requiresCreativeFormat) {
    return 'none';
  }
  return 'weak';
}
```

### 条件组装
```typescript
// 根据不同条件添加额外的prompt片段
if (data.hasMultipleSources) {
  sections.push('<SYNTHESIS_INSTRUCTION>Synthesize information from multiple sources effectively.</SYNTHESIS_INSTRUCTION>');
}

if (data.hasImages) {
  sections.push('<IMAGE_INTEGRATION>Include relevant images with detailed captions.</IMAGE_INTEGRATION>');
}
```

## 调试和优化

### 查看组装结果
在开发环境中，可以在控制台查看最终组装的prompt：
```typescript
console.log('Final assembled prompt:', finalPrompt);
```

### 性能优化
- 缓存常用的prompt片段
- 避免重复的字符串操作
- 使用模板字符串提高可读性

### 质量保证
- 验证所有必需的数据都已包含
- 检查prompt的总长度是否在合理范围内
- 确保格式指导的清晰性和准确性

## 总结

分层组装式Prompt架构的优势：
1. **模块化**: 每个层次职责明确，易于维护
2. **灵活性**: 可以根据需要组合不同的层次
3. **可扩展性**: 添加新格式或功能变得简单
4. **一致性**: 所有格式都使用相同的数据基础
5. **可控性**: 通过权重机制精确控制各部分的影响

这种设计使得Deep Research能够支持多样化的报告需求，同时保持系统的稳定性和可维护性。
