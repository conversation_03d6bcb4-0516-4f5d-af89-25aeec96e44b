import { useCallback, useMemo } from 'react';
import { generateText } from 'ai';
import { useTaskStore } from '@/store/task';
import useAiProvider from '@/hooks/useAiProvider';
import useWebSearch from '@/hooks/useWebSearch';
import { 
  AgentId, 
  AgentActionName, 
  AgentDiscussion, 
  ChapterCollectedInfo,
  ChapterReport,
  ChapterStatus,
  ResearchPhase
} from '@/types/chapter-research';
import { 
  parsePlanToChapters, 
  validateChapters, 
  getChapterCollectedSummary 
} from '@/utils/chapter-research';
import { 
  generateAgentPrompt, 
  parseAgentResponse,
  repairJsonString
} from '@/utils/chapter-research/prompts';
import { processSearchResultPrompt } from '@/utils/deep-research/prompts';
import { getSystemPrompt } from '@/utils/deep-research/prompts';
import { 
  chapterSummaryPrompt
} from '@/constants/prompts';
import { 
  generateChapterReportPrompt,
  generateFirstChapterPreviewPrompt 
} from '@/utils/chapter-research/report-prompts';

export function useChapterResearch() {
  const taskStore = useTaskStore();
  const { createModelProvider, getModel } = useAiProvider();
  const { search: performSearch } = useWebSearch();

  // 获取章节研究状态
  const chapterResearch = taskStore.chapterResearch;

  // 启动章节式研究
  const startChapterResearch = useCallback(async (planText: string) => {
    console.log('🚀 [章节研究] 启动章节式研究');
    
    try {
      // 解析计划为章节
      const chapters = parsePlanToChapters(planText);
      
      if (!validateChapters(chapters)) {
        throw new Error('章节解析验证失败');
      }

      // 初始化章节研究状态
      taskStore.initChapterResearch(taskStore.query, chapters);
      
      console.log('✅ [章节研究] 初始化完成，章节数:', chapters.length);
      
      // 初始化完成，不自动开始讨论
      console.log('📋 [章节研究] 章节列表已准备就绪，等待用户配置和启动');
      
      return true;
    } catch (error) {
      console.error('❌ [章节研究] 启动失败:', error);
      // 确保失败时清理状态
      taskStore.resetChapterResearch();
      return false;
    }
  }, [taskStore]);

  // 开始研究指定章节
  const startChapterDiscussion = useCallback(async (chapterId: string) => {
    console.log('💬 [章节讨论] 开始章节讨论:', chapterId);

    // 从最新的状态中获取章节信息
    const currentState = taskStore.chapterResearch;
    const chapter = currentState.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      console.error(`❌ [章节讨论] 找不到章节: ${chapterId}，可用章节:`, currentState.chapters.map(c => c.id));
      throw new Error(`找不到章节: ${chapterId}`);
    }

    // 清理该章节的旧讨论记录，防止数据污染
    const oldDiscussions = currentState.discussions.filter(d => d.chapterId === chapterId);
    if (oldDiscussions.length > 0) {
      console.log(`🧹 [数据清理] 清理章节${chapterId}的${oldDiscussions.length}条旧讨论记录`);
      // 这里需要添加清理旧discussions的方法，暂时通过重置来处理
    }

    // 更新章节状态
    taskStore.setCurrentChapter(chapterId);
    taskStore.updateChapterStatus(chapterId, ChapterStatus.DISCUSSING);
    taskStore.setChapterResearchRunning(true);

    try {
      // 从Alpha开始第一轮高层级讨论
      await conductAgentDiscussion(chapterId, 'Alpha', 'HIGH_LEVEL_PROPOSE', 1, 1);
    } catch (error) {
      console.error('❌ [章节讨论] 开始讨论失败:', error);
      taskStore.setChapterResearchRunning(false);
      throw error;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskStore]);

  // 进行智能体对话
  const conductAgentDiscussion = useCallback(async (
    chapterId: string,
    agentId: AgentId,
    expectedAction: AgentActionName,
    roundNumber: number,
    turnNumber: number
  ) => {
    console.log(`🤖 [智能体对话] ${agentId} 执行 ${expectedAction}, 轮次: ${roundNumber}-${turnNumber}`);
    console.log(`🤖 [智能体对话] 调用时刻的状态检查...`);
    
    // 从最新状态获取章节信息，避免状态同步延迟问题
    const currentState = useTaskStore.getState().chapterResearch;
    const currentDiscussions = currentState.discussions.filter(d => d.chapterId === chapterId);
    console.log(`📚 [调用前状态] 该章节现有讨论数: ${currentDiscussions.length}`);
    console.log(`📚 [调用前状态] 讨论详情:`, currentDiscussions.map(d => `R${d.roundNumber}-T${d.turnNumber}-${d.agentId}-${d.actionName}`));
    const chapter = currentState.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      console.error(`❌ [智能体对话] 找不到章节: ${chapterId}，可用章节:`, currentState.chapters.map(c => c.id));
      throw new Error(`找不到章节: ${chapterId}`);
    }

    try {
      // 收集上下文信息，传递当前回合号以便正确过滤
      console.log(`📋 [上下文准备] 即将收集章节${chapterId}轮次${roundNumber}-${turnNumber}的上下文，代理${agentId}动作${expectedAction}`);
      const contextData = collectDiscussionContext(chapterId, roundNumber, turnNumber);
      
      // 生成prompt
      const prompt = generateAgentPrompt(
        agentId,
        expectedAction,
        chapter.goal,
        currentState.userQuery,
        contextData
      );
      
      console.log(`📄 [Prompt检查] 生成的prompt长度: ${prompt.length}`);
      console.log(`📄 [Prompt检查] 上下文数据:`, {
        collectedInfoSection: contextData.collectedInfoSection?.substring(0, 100) + '...',
        currentRoundDiscussions: contextData.currentRoundDiscussions?.substring(0, 100) + '...',
        currentQueryDiscussions: contextData.currentQueryDiscussions?.substring(0, 100) + '...',
        hasHighLevelConsensus: !!contextData.highLevelConsensus
      });

      console.log(`🤖 [AI调用] 准备调用AI模型，代理: ${agentId}, 动作: ${expectedAction}`);

      // 调用AI - 添加详细调试
      console.log('🔧 [AI调试] 获取模型配置...');
      const modelConfig = getModel();
      console.log('🔧 [AI调试] 模型配置:', {
        thinkingModel: modelConfig.thinkingModel,
        networkingModel: modelConfig.networkingModel,
        hasThinkingModel: !!modelConfig.thinkingModel,
        thinkingModelType: typeof modelConfig.thinkingModel
      });

      console.log('🔧 [AI调试] 创建模型提供者...');
      let model;
      try {
        model = await createModelProvider(modelConfig.thinkingModel);
        console.log('🔧 [AI调试] 模型创建成功:', {
          modelExists: !!model,
          modelType: typeof model,
          modelConstructor: model?.constructor?.name,
          modelKeys: model ? Object.keys(model).slice(0, 5) : []
        });
      } catch (modelError) {
        console.error('❌ [AI调试] 模型创建失败:', modelError);
        const errorMessage = modelError instanceof Error ? modelError.message : String(modelError);
        throw new Error(`模型创建失败: ${errorMessage}`);
      }

      if (!model) {
        throw new Error('模型创建失败，createModelProvider返回了空值');
      }

      console.log('🔧 [AI调试] 开始调用generateText...');
      console.log('🔧 [AI调试] Prompt长度:', prompt.length);
      console.log('🔧 [AI调试] Prompt前500字符:', prompt.substring(0, 500));
      
      let responseText: string = '';
      try {
        const result = await generateText({
          model,
          prompt,
          temperature: 0.7,
          maxTokens: 16380, // 设置32K tokens确保智能体对话完整
        });
        
        console.log('🔧 [AI调试] generateText返回结果:', {
          resultExists: !!result,
          resultType: typeof result,
          resultKeys: result ? Object.keys(result) : [],
          hasText: !!result?.text,
          textType: typeof result?.text,
          textLength: result?.text?.length || 0,
          hasResponse: !!(result as any)?.response,
          hasContent: !!(result as any)?.content,
          hasCompletion: !!(result as any)?.completion,
          fullResult: JSON.stringify(result).substring(0, 500)
        });
        
        // 尝试从不同字段获取文本
        if (result.text) {
          responseText = result.text;
        } else if ((result as any).response) {
          console.log('⚠️ [AI调试] text字段不存在，尝试使用response字段');
          responseText = (result as any).response;
        } else if ((result as any).content) {
          console.log('⚠️ [AI调试] text字段不存在，尝试使用content字段');
          responseText = (result as any).content;
        } else if ((result as any).completion) {
          console.log('⚠️ [AI调试] text字段不存在，尝试使用completion字段');
          responseText = (result as any).completion;
        }
        
      } catch (genError: any) {
        console.error('❌ [AI调试] generateText抛出错误:', genError);
        console.error('❌ [AI调试] 错误详情:', {
          message: genError.message,
          stack: genError.stack,
          name: genError.name
        });
        throw genError;
      }

      console.log(`✅ [AI响应] 收到AI响应，长度: ${responseText.length}`);
      console.log(`📄 [AI响应内容]`, responseText ? responseText.substring(0, 300) + '...' : '(空响应)');

      // 检查AI响应是否为空
      if (!responseText || responseText.trim().length === 0) {
        throw new Error('AI返回了空响应，请检查模型配置和API连接');
      }

      // 解析响应 - 添加重试机制
      let parsedResponse: ReturnType<typeof parseAgentResponse> | null = null;
      let retryCount = 0;
      const maxRetries = 3;
      
      while (retryCount < maxRetries) {
        try {
          if (retryCount < 2) {
            // 第1-2次重试：直接解析原始响应
            console.log(`🔄 [解析重试] 第${retryCount + 1}次尝试解析AI响应...`);
            parsedResponse = parseAgentResponse(responseText);
          } else {
            // 第3次重试：使用修复后的响应
            console.log(`🔧 [解析重试] 第${retryCount + 1}次尝试，使用JSON修复...`);
            const repairedResponse = repairJsonString(responseText);
            console.log(`🔧 [JSON修复] 修复前长度: ${responseText.length}, 修复后长度: ${repairedResponse.length}`);
            parsedResponse = parseAgentResponse(repairedResponse);
          }
          
          // 解析成功，跳出重试循环
          console.log(`✅ [解析成功] 第${retryCount + 1}次尝试成功，动作: ${parsedResponse.action.actionName}`);
          break;
          
        } catch (parseError) {
          retryCount++;
          console.warn(`⚠️ [解析重试] 第${retryCount}次解析失败:`, parseError instanceof Error ? parseError.message : parseError);
          
          if (retryCount >= maxRetries) {
            // 重试次数用尽，记录详细错误信息
            console.error(`❌ [解析失败] 已重试${maxRetries}次，全部失败`);
            console.error(`❌ [完整响应] AI响应内容:`, responseText);
            throw parseError;
          }
          
          // 短暂延迟后重试
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      // 确保parsedResponse不为null（理论上不会到达这里，因为上面会throw error）
      if (!parsedResponse) {
        throw new Error('解析失败：parsedResponse为null');
      }
      
      console.log(`🔍 [解析结果] 动作: ${parsedResponse.action.actionName}, payload键:`, Object.keys(parsedResponse.action.payload));

      // 保存对话记录
      const discussion: AgentDiscussion = {
        id: `discussion-${chapterId}-${roundNumber}-${turnNumber}-${Date.now()}`,
        chapterId,
        roundNumber,
        turnNumber,
        agentId,
        actionName: parsedResponse.action.actionName,
        payload: parsedResponse.action.payload,
        thought: parsedResponse.thought,
        timestamp: Date.now(),
      };

      console.log(`💾 [保存前] 准备保存讨论记录:`, {
        chapterId,
        roundNumber, 
        turnNumber,
        agentId,
        actionName: parsedResponse.action.actionName,
        hasPayload: !!parsedResponse.action.payload,
        payloadKeys: Object.keys(parsedResponse.action.payload || {}),
        queriesCount: parsedResponse.action.payload?.queries?.length || 0,
        discussionObject: discussion // 添加完整的discussion对象检查
      });
      
      console.log(`💾 [保存前] Discussion对象完整内容:`, JSON.stringify(discussion, null, 2));
      
      taskStore.addAgentDiscussion(discussion);
      console.log(`📝 [对话记录] 已保存${agentId}的对话记录: 轮次${roundNumber}-${turnNumber}, 动作${parsedResponse.action.actionName}`);
      
      // 等待状态更新生效
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 延长等待时间，确保persist完成
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // 使用最新状态快照避免persist干扰
      const currentStateAfterSave = useTaskStore.getState().chapterResearch;
      const discussionsCount = currentStateAfterSave.discussions.length;
      const chapterDiscussions = currentStateAfterSave.discussions.filter(d => d.chapterId === chapterId);
      
      console.log(`🔍 [状态验证] 保存后总讨论数: ${discussionsCount}`);
      console.log(`🔍 [状态验证] 该章节讨论数: ${chapterDiscussions.length}`);
      console.log(`🔍 [状态验证] 所有讨论:`, currentStateAfterSave.discussions.map(d => `${d.chapterId}-R${d.roundNumber}-T${d.turnNumber}-${d.agentId}-${d.actionName}`));
      
      // 如果状态验证失败，说明persist有问题，直接禁用persist进行状态更新
      if (discussionsCount === 0 || chapterDiscussions.length === 0) {
        console.error(`🚨 [Persist Bug] 检测到persist机制干扰状态更新，禁用persist重新添加讨论...`);
        
        // 使用非persist方式强制更新状态 - 直接操作内存状态
        const currentState = taskStore; // 使用当前hook的taskStore引用
        const existingDiscussions = currentState.chapterResearch.discussions;
        
        // 检查是否已存在该讨论，避免重复添加
        const discussionExists = existingDiscussions.some(d => d.id === discussion.id);
        
        if (!discussionExists) {
          // 直接修改状态，跳过persist
          const newChapterResearch = {
            ...currentState.chapterResearch,
            discussions: [...existingDiscussions, discussion],
          };
          
          // 强制更新整个chapterResearch状态
          Object.assign(currentState.chapterResearch, newChapterResearch);
          
          console.log(`🔄 [强制修复] 直接修改状态，新讨论数: ${newChapterResearch.discussions.length}`);
        } else {
          console.log(`⚠️ [重复检测] 讨论${discussion.id}已存在，跳过添加`);
        }
      }
      
      const savedDiscussions = currentStateAfterSave.discussions.filter(d => d.chapterId === chapterId);
      console.log(`🔍 [验证保存] 该章节现有${savedDiscussions.length}个讨论记录:`, savedDiscussions.map(d => `R${d.roundNumber}-T${d.turnNumber}-${d.agentId}-${d.actionName}`));
      
      // 特别检查查询相关的讨论
      const queryDiscussions = savedDiscussions.filter(d => d.actionName === 'QUERY_PROPOSE' || d.actionName === 'EXECUTE_QUERIES');
      console.log(`🔍 [验证查询] 该章节现有${queryDiscussions.length}个查询相关讨论:`, queryDiscussions.map(d => `R${d.roundNumber}-T${d.turnNumber}-${d.agentId}-${d.actionName}-(${d.payload.queries?.length || 0}个查询)`));

      // 根据动作类型决定下一步
      await handleAgentAction(chapterId, discussion, roundNumber, turnNumber);

    } catch (error) {
      console.error('❌ [智能体对话] 对话失败:', error);
      console.error('错误详情:', error);
      // 设置研究为失败状态
      taskStore.setChapterResearchRunning(false);
      throw error;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskStore, createModelProvider, getModel]);

  // 处理智能体动作
  const handleAgentAction = useCallback(async (
    chapterId: string,
    discussion: AgentDiscussion,
    roundNumber: number,
    turnNumber: number
  ) => {
    const { actionName, agentId } = discussion;
    
    switch (actionName) {
      case 'HIGH_LEVEL_PROPOSE':
        // 检查高层级对话次数限制
        if (checkTurnLimits(chapterId, roundNumber, turnNumber, 'high_level')) {
          await forceNextPhase(chapterId, roundNumber, 'high_level', discussion);
        } else {
          // 继续高层级对话 - 添加延迟确保状态更新完成
          const nextAgent: AgentId = agentId === 'Alpha' ? 'Beta' : 'Alpha';
          setTimeout(async () => {
            try {
              await conductAgentDiscussion(chapterId, nextAgent, 'HIGH_LEVEL_AGREE', roundNumber, turnNumber + 1);
            } catch (error) {
              console.error('❌ [高层级阶段] 继续对话失败:', error);
              taskStore.setChapterResearchRunning(false);
            }
          }, 100); // 短暂延迟让状态更新生效
        }
        break;

      case 'HIGH_LEVEL_AGREE':
        // 高层级达成共识，进入查询阶段
        const consensus = discussion.payload.agreement || '已达成研究方向共识';
        taskStore.setChapterConsensus(chapterId, consensus);
        taskStore.updateChapterPhase(chapterId, ResearchPhase.QUERY);
        
        // 开始查询讨论 - 添加延迟确保状态更新完成
        setTimeout(async () => {
          try {
            await conductAgentDiscussion(chapterId, 'Alpha', 'QUERY_PROPOSE', roundNumber, 1);
          } catch (error) {
            console.error('❌ [进入查询阶段] 启动查询讨论失败:', error);
            taskStore.setChapterResearchRunning(false);
          }
        }, 100); // 短暂延迟让状态更新生效
        break;

      case 'QUERY_PROPOSE':
        // 检查查询对话次数限制
        if (checkTurnLimits(chapterId, roundNumber, turnNumber, 'query')) {
          await forceNextPhase(chapterId, roundNumber, 'query', discussion);
        } else {
          // 继续查询对话 - 添加延迟确保状态更新完成
          const reviewAgent: AgentId = agentId === 'Alpha' ? 'Beta' : 'Alpha';
          console.log(`🔄 [查询继续] ${agentId}(QUERY_PROPOSE) -> ${reviewAgent}(EXECUTE_QUERIES), 轮次: ${roundNumber}-${turnNumber+1}`);
          console.log(`🔄 [查询继续] 当前查询payload:`, discussion.payload.queries);
          
          setTimeout(async () => {
            try {
              console.log(`⏰ [延迟执行] 开始调用${reviewAgent}进行EXECUTE_QUERIES`);
              await conductAgentDiscussion(chapterId, reviewAgent, 'EXECUTE_QUERIES', roundNumber, turnNumber + 1);
            } catch (error) {
              console.error('❌ [查询阶段] 继续对话失败:', error);
              taskStore.setChapterResearchRunning(false);
            }
          }, 100); // 短暂延迟让状态更新生效
        }
        break;

      case 'EXECUTE_QUERIES':
        // 执行搜索查询
        const queries = discussion.payload.queries || [];
        console.log(`📝 [查询验证] 获取到${queries.length}个查询:`, queries);
        
        // 将查询添加到历史记录
        if (queries.length > 0) {
          const historicalQueries = queries.map(q => ({
            ...q,
            fromRound: roundNumber
          }));
          taskStore.addChapterHistoricalQueries(chapterId, historicalQueries);
        }
        
        // 先执行搜索，然后决定下一步
        setTimeout(async () => {
          try {
            if (queries.length > 0) {
              await executeSearchQueries(chapterId, queries, roundNumber);
            } else {
              console.warn('⚠️ [查询验证] 没有获取到有效查询，尝试从discussion中提取');
              // 尝试从discussion的thought或其他字段提取查询
            }
            
            // 搜索完成后，检查是否需要继续下一轮
            const currentConfig = taskStore.chapterResearch.config;
            console.log(`🔄 [轮数控制] 当前轮次: ${roundNumber}, 最大轮数: ${currentConfig.maxRounds}`);
            
            if (roundNumber < currentConfig.maxRounds) {
              console.log(`➡️ [轮数控制] 开始第${roundNumber + 1}轮讨论`);
              // 开始新一轮高层级讨论，使用收集到的信息作为上下文
              setTimeout(() => {
                conductAgentDiscussion(chapterId, 'Alpha', 'HIGH_LEVEL_PROPOSE', roundNumber + 1, 1)
                  .catch(error => {
                    console.error('❌ [新轮次] 启动新轮次失败:', error);
                    taskStore.setChapterResearchRunning(false);
                  });
              }, 1000); // 给搜索结果一点时间保存
            } else {
              console.log(`✅ [轮数控制] 达到最大轮数，完成章节信息收集`);
              // 完成章节信息收集，不生成报告
              await completeChapterInfoCollection(chapterId);
            }
          } catch (error) {
            console.error('❌ [搜索和后续处理] 执行失败:', error);
            taskStore.setChapterResearchRunning(false);
          }
        }, 200);
        break;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskStore]);

  // 生成搜索结果总结 
  const generateSearchResultSummary = useCallback(async (
    query: string,
    researchGoal: string,
    sources: Source[],
    chapterId: string,
    roundNumber: number
  ): Promise<string> => {
    console.log(`📝 [搜索总结] 开始总结搜索结果: "${query}"`);
    
    try {
      // 注册全局引用编号
      const urlToIndexMap = taskStore.registerMultipleCitations(sources, chapterId, roundNumber);
      
      // 构建带全局编号的 sources 字符串，同时创建本地编号到全局编号的映射
      const localToGlobalMap = new Map<number, number>();
      const sourcesWithLocalIndex = sources.map((source, index) => {
        const globalIndex = urlToIndexMap.get(source.url)!;
        const localIndex = index + 1; // 本地编号从1开始
        localToGlobalMap.set(localIndex, globalIndex);
        return `<source index="${localIndex}" url="${source.url}">\n${source.title}\n</source>`;
      }).join('\n');
      
      console.log(`🔢 [全局编号] 为${sources.length}个来源分配了全局编号:`, 
        Array.from(urlToIndexMap.entries()).map(([url, index]) => `[${index}] ${url.substring(0, 50)}...`));
      console.log(`🔄 [编号映射] 本地编号到全局编号映射:`, 
        Array.from(localToGlobalMap.entries()).map(([local, global]) => `[${local}] -> [${global}]`));
      
      // 修改prompt，明确告诉AI使用从1开始的连续编号
      const customCitationPrompt = `Citation Rules:
- Please cite the sources using sequential numbers starting from [1]
- Use format [1], [2], [3]... in order of the sources provided
- The first source in the list should be cited as [1], the second as [2], and so on
- If citing multiple sources, list them like [1][2][3]`;
      
      const model = await createModelProvider(getModel().networkingModel);
      const result = await generateText({
        model,
        system: getSystemPrompt(),
        prompt: `${processSearchResultPrompt(query, researchGoal, sources, false)}\n\n${customCitationPrompt}\n\n<SOURCES>\n${sourcesWithLocalIndex}\n</SOURCES>`,
        temperature: 0.6,
        maxTokens: 16380, // 增加到64K tokens确保信息收集总结完整
      });
      
      // 替换AI生成的本地编号为全局编号
      let processedText = result.text;
      console.log(`🔄 [引用替换] 开始替换引用编号...`);
      
      // 使用正则表达式匹配所有 [数字] 格式的引用
      processedText = processedText.replace(/\[(\d+)\]/g, (match, localNum) => {
        const localIndex = parseInt(localNum);
        const globalIndex = localToGlobalMap.get(localIndex);
        if (globalIndex) {
          console.log(`  替换: [${localIndex}] -> [${globalIndex}]`);
          return `[${globalIndex}]`;
        }
        // 如果找不到映射，保持原样（可能是其他用途的方括号）
        return match;
      });
      
      console.log(`✅ [搜索总结] 总结完成并完成引用替换，长度: ${processedText.length}`);
      return processedText;
    } catch (error) {
      console.error(`❌ [搜索总结] 总结失败:`, error);
      // 如果总结失败，返回原始内容
      return sources.map(s => s.content).join('\n\n');
    }
  }, [createModelProvider, getModel, taskStore]);

  // 执行搜索查询
  const executeSearchQueries = useCallback(async (
    chapterId: string,
    queries: Array<{query: string; researchGoal: string}>,
    roundNumber: number
  ) => {
    console.log(`🔍 [搜索执行] 执行${queries.length}个查询，章节: ${chapterId}`);
    
    taskStore.updateChapterPhase(chapterId, ResearchPhase.SEARCHING);
    
    // 获取配置，检查是否启用信息总结
    const { config } = taskStore.chapterResearch;
    const { enableInfoSummarization } = config;
    
    console.log(`⚙️ [搜索配置] 信息总结模式: ${enableInfoSummarization ? '启用' : '禁用'}`);
    
    const searchResults = [];
    const summaryPromises: Array<Promise<void>> = []; // 用于异步流程控制
    
    // 初始化搜索进度
    taskStore.updateSearchProgress(chapterId, {
      totalQueries: queries.length,
      completedQueries: 0,
      summaryInProgress: enableInfoSummarization,
    });

    for (const queryInfo of queries) {
      try {
        console.log(`🔍 [搜索] 查询内容:`, queryInfo);
        
        // 处理不同的查询格式
        let queryText: string;
        let researchGoal: string;
        
        if (typeof queryInfo === 'string') {
          // 如果是字符串，直接使用
          queryText = queryInfo;
          researchGoal = '章节研究查询';
        } else if (queryInfo && typeof queryInfo.query === 'string') {
          // 如果是对象，提取query字段 
          queryText = queryInfo.query;
          researchGoal = queryInfo.researchGoal || '章节研究查询';
        } else {
          console.error('❌ [搜索] 无效的查询格式:', queryInfo);
          continue;
        }
        
        console.log(`📝 [搜索] 执行查询: "${queryText}"`);
        // 更新当前查询
        taskStore.updateSearchProgress(chapterId, { currentQuery: queryText });
        
        const searchResult = await performSearch(queryText);

        if (searchResult.sources && searchResult.sources.length > 0) {
          // 创建搜索结果条目
          const searchResultEntry = {
            query: queryText,
            researchGoal: researchGoal,
            results: '', // 将在后面填充
            sources: searchResult.sources,
            images: searchResult.images || [], // 新增：保留图片信息
          };

          if (enableInfoSummarization) {
            // 异步总结模式：创建promise但不等待
            const summaryPromise = generateSearchResultSummary(
              queryText,
              researchGoal,
              searchResult.sources,
              chapterId,
              roundNumber
            ).then((summary) => {
              searchResultEntry.results = summary;
              console.log(`✅ [异步总结] 查询"${queryText}"总结完成`);
            }).catch((error) => {
              console.error(`❌ [异步总结] 查询"${queryText}"总结失败:`, error);
              // 总结失败时使用原始内容
              searchResultEntry.results = searchResult.sources.map(r => r.content).join('\n\n');
            }).finally(() => {
              // 更新完成进度
              const currentProgress = taskStore.chapterResearch.chapters.find(c => c.id === chapterId)?.searchProgress;
              if (currentProgress) {
                taskStore.updateSearchProgress(chapterId, {
                  completedQueries: currentProgress.completedQueries + 1,
                });
              }
            });
            
            summaryPromises.push(summaryPromise);
          } else {
            // 快速模式：直接使用原始内容
            searchResultEntry.results = searchResult.sources.map(r => r.content).join('\n\n');
            // 立即更新完成进度
            const currentProgress = taskStore.chapterResearch.chapters.find(c => c.id === chapterId)?.searchProgress;
            if (currentProgress) {
              taskStore.updateSearchProgress(chapterId, {
                completedQueries: currentProgress.completedQueries + 1,
              });
            }
          }

          searchResults.push(searchResultEntry);

          // 将搜索结果添加为搜索任务（复用现有系统）
          const searchTask = {
            query: queryText,
            researchGoal: researchGoal,
            language: 'chinese' as const,
            state: 'completed' as const,
            taskType: 'chapter-research' as const,
            chapterId,
            roundNumber,
          };

          taskStore.addAutoResearchTask(searchTask as any);
        }
      } catch (error) {
        const queryText = typeof queryInfo === 'string' ? queryInfo : (queryInfo?.query || 'unknown');
        console.error(`❌ [搜索] 查询失败: ${queryText}`, error);
      }
    }

    // 异步流程控制：根据模式决定是否等待总结完成
    if (enableInfoSummarization && summaryPromises.length > 0) {
      console.log(`⏳ [异步等待] 等待${summaryPromises.length}个总结任务完成...`);
      await Promise.all(summaryPromises);
      console.log(`✅ [异步等待] 所有总结任务已完成`);
    }

    // 汇总所有搜索结果中的图片
    const aggregatedImages = searchResults.reduce((acc, result) => {
      return acc.concat(result.images || []);
    }, [] as ImageSource[]);

    // 保存收集的信息
    const collectedInfo: ChapterCollectedInfo = {
      chapterId,
      roundNumber,
      searchResults,
      summary: `第${roundNumber}轮收集到${searchResults.length}个查询的信息${enableInfoSummarization ? '（已总结）' : '（原始内容）'}`,
      aggregatedImages, // 新增：汇总的图片信息
    };

    taskStore.addChapterCollectedInfo(collectedInfo);
    
    // 搜索完成，清理进度状态
    taskStore.updateSearchProgress(chapterId, {
      currentQuery: undefined,
      summaryInProgress: false,
    });
    
    console.log(`✅ [搜索完成] 章节${chapterId}第${roundNumber}轮搜索完成 (模式: ${enableInfoSummarization ? 'AI总结' : '快速原始'})`);
  }, [taskStore, performSearch, generateSearchResultSummary]);

  // 检查对话次数是否超过限制
  const checkTurnLimits = useCallback((
    chapterId: string, 
    roundNumber: number, 
    turnNumber: number,
    phase: 'high_level' | 'query'
  ): boolean => {
    const currentConfig = taskStore.chapterResearch.config;
    const limit = phase === 'high_level' ? currentConfig.highLevelTurns : currentConfig.queryTurns;
    
    console.log(`🔍 [对话限制检查] 章节${chapterId} 轮次${roundNumber} ${phase}阶段 回合${turnNumber}/${limit}`);
    
    return turnNumber >= limit;
  }, [taskStore]);

  // 生成第一章节预览报告（无参考来源）
  const generateFirstChapterPreview = useCallback(async (chapterId: string): Promise<string> => {
    console.log('🔄 [第一章节] 开始生成预览报告');
    
    const currentState = taskStore.chapterResearch;
    const chapter = currentState.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      console.error(`❌ [第一章节] 找不到章节: ${chapterId}`);
      return '';
    }
    
    try {
      // 获取第一章节的所有收集信息
      const allInfo = getChapterCollectedSummary(
        chapterId, 
        currentState.collectedInfo
      );
      
      // 生成预览报告的prompt
      const previewPrompt = generateFirstChapterPreviewPrompt(
        chapter.title,
        chapter.goal,
        allInfo
      );
      
      const model = await createModelProvider(getModel().thinkingModel);
      const result = await generateText({
        model,
        prompt: previewPrompt,
        temperature: 0.6,
        maxTokens: 8192, // 控制长度
      });
      
      console.log(`✅ [第一章节] 预览报告生成完成，长度: ${result.text.length}`);
      return result.text;
      
    } catch (error) {
      console.error('❌ [第一章节] 生成预览报告失败:', error);
      return '';
    }
  }, [taskStore, createModelProvider, getModel]);

  // 完成章节信息收集
  const completeChapterInfoCollection = useCallback(async (chapterId: string) => {
    console.log(`📋 [信息收集完成] 章节信息收集完成: ${chapterId}`);
    
    const currentState = taskStore.chapterResearch;
    const chapter = currentState.chapters.find(c => c.id === chapterId)!;
    
    // 标记章节为信息收集完成状态
    taskStore.updateChapterStatus(chapterId, ChapterStatus.COMPLETED);
    console.log(`✅ [信息收集] 章节${chapter.title}信息收集完成`);

    try {
      // 检查下一步：启动下一章节
      const updatedState = taskStore.chapterResearch;
      const currentChapterIndex = updatedState.chapters.findIndex(c => c.id === chapterId);
      
      // 如果是第一章节完成，生成预览报告
      if (currentChapterIndex === 0) {
        console.log('🔄 [第一章节] 第一章节信息收集完成，生成预览报告供其他章节参考');
        const firstChapterPreview = await generateFirstChapterPreview(chapterId);
        
        if (firstChapterPreview) {
          // 存储到taskStore中供其他章节使用
          taskStore.setFirstChapterPreview(firstChapterPreview);
          console.log(`📝 [第一章节] 预览报告已保存，字数: ${firstChapterPreview.length}`);
        }
      }
      
      const nextChapter = updatedState.chapters[currentChapterIndex + 1];
      
      if (nextChapter && nextChapter.status === ChapterStatus.PENDING) {
        // 有下一个章节，立即启动它
        console.log(`➡️ [章节进度] 启动下一章节信息收集: ${nextChapter.title}`);
        setTimeout(async () => {
          try {
            await startChapterDiscussion(nextChapter.id);
          } catch (error) {
            console.error('❌ [下一章节] 启动下一章节失败:', error);
            taskStore.setChapterResearchRunning(false);
          }
        }, 1000); // 给状态更新一点时间
      } else {
        // 所有章节信息收集都完成了
        const allInfoCollected = updatedState.chapters.every(c => 
          c.status === ChapterStatus.COMPLETED
        );
        
        if (allInfoCollected) {
          console.log(`🎯 [信息收集完成] 所有章节信息收集已完成，等待用户生成报告`);
          taskStore.setChapterResearchRunning(false);
        } else {
          console.log(`📊 [信息收集状态] 当前章节完成，但还有其他章节未完成`);
          taskStore.setChapterResearchRunning(false);
        }
      }

    } catch (error) {
      console.error('❌ [信息收集完成] 处理失败:', error);
      taskStore.setChapterResearchRunning(false);
      throw error;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps  
  }, [taskStore]);

  // 强制进入下一阶段
  const forceNextPhase = useCallback(async (
    chapterId: string,
    roundNumber: number,
    currentPhase: 'high_level' | 'query',
    lastDiscussion?: AgentDiscussion
  ) => {
    console.log(`🚨 [强制转换] 章节${chapterId} 轮次${roundNumber} 从${currentPhase}阶段强制转换`);
    
    if (currentPhase === 'high_level') {
      // 强制达成高层级共识，进入查询阶段
      const forcedConsensus = '已达到高层级对话次数限制，基于当前讨论进入查询阶段';
      taskStore.setChapterConsensus(chapterId, forcedConsensus);
      taskStore.updateChapterPhase(chapterId, ResearchPhase.QUERY);
      await conductAgentDiscussion(chapterId, 'Alpha', 'QUERY_PROPOSE', roundNumber, 1);
    } else if (currentPhase === 'query') {
      // 强制执行查询
      const queries = lastDiscussion?.payload?.queries || [];
      if (queries.length > 0) {
        await executeSearchQueries(chapterId, queries, roundNumber);
      }
      
      // 强制执行后，同样需要检查轮数
      const currentConfig = taskStore.chapterResearch.config;
      if (roundNumber < currentConfig.maxRounds) {
        setTimeout(() => {
          conductAgentDiscussion(chapterId, 'Alpha', 'HIGH_LEVEL_PROPOSE', roundNumber + 1, 1)
            .catch(error => {
              console.error('❌ [强制下一轮] 启动新轮次失败:', error);
              taskStore.setChapterResearchRunning(false);
            });
        }, 1000);
      } else {
        await completeChapterInfoCollection(chapterId);
      }
    }
  }, [taskStore, conductAgentDiscussion, executeSearchQueries, completeChapterInfoCollection]);

  // 生成单个章节报告
  const generateChapterReport = useCallback(async (chapterId: string) => {
    console.log(`📝 [章节报告] 开始生成章节报告: ${chapterId}`);
    
    const currentState = taskStore.chapterResearch;
    const chapter = currentState.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      throw new Error(`找不到章节: ${chapterId}`);
    }

    try {
      // 获取该章节的所有收集信息
      const allInfo = getChapterCollectedSummary(chapterId, currentState.collectedInfo);
      
      // 收集所有sources和images
      const allSources: Source[] = [];
      const allImages: ImageSource[] = [];
      
      currentState.collectedInfo
        .filter(info => info.chapterId === chapterId)
        .forEach(info => {
          info.searchResults.forEach(result => {
            allSources.push(...result.sources);
            if (result.images) {
              allImages.push(...result.images);
            }
          });
          // 添加汇总的图片
          if (info.aggregatedImages) {
            allImages.push(...info.aggregatedImages);
          }
        });

      // 去重处理
      const uniqueSources = allSources.filter((source, index, self) => 
        index === self.findIndex(s => s.url === source.url)
      );
      const uniqueImages = allImages.filter((image, index, self) => 
        index === self.findIndex(img => img.url === image.url)
      );

      // 为所有 sources 注册全局编号
      const globalCitationsMap = taskStore.registerMultipleCitations(uniqueSources, chapterId);
      
      console.log(`🔢 [章节报告] 为章节${chapter.title}的${uniqueSources.length}个来源分配了全局编号:`, 
        Array.from(globalCitationsMap.entries()).map(([url, index]) => `[${index}] ${url.substring(0, 50)}...`));

      // 使用标准化的报告生成prompt（带全局编号）
      const reportPrompt = generateChapterReportPrompt(
        chapter.title,
        chapter.goal,
        allInfo,
        uniqueSources,
        uniqueImages,
        true, // enableReferences
        currentState.config.enableImages, // 根据配置决定是否包含图片
        globalCitationsMap // 传递全局编号映射
      );

      const model = await createModelProvider(getModel().thinkingModel);
      const result = await generateText({
        model,
        prompt: reportPrompt,
        temperature: 0.6,
        maxTokens: 16380, // 增加到64K tokens确保章节报告完整
      });
      const reportContent = result.text;

      // 保存章节报告
      const chapterReport: ChapterReport = {
        chapterId,
        title: chapter.title,
        content: reportContent,
        sources: uniqueSources,
        images: uniqueImages, // 新增：保存使用的图片
        generatedAt: Date.now(),
      };

      taskStore.addChapterReport(chapterReport);
      console.log(`✅ [章节报告] 章节报告生成完成: ${chapter.title}`);
      
      return chapterReport;

    } catch (error) {
      console.error('❌ [章节报告] 生成报告失败:', error);
      throw error;
    }
  }, [taskStore, createModelProvider, getModel]);

  // 生成所有章节报告
  const generateAllChapterReports = useCallback(async () => {
    console.log('📝 [批量报告] 开始生成所有章节报告');
    
    const currentState = taskStore.chapterResearch;
    const completedChapters = currentState.chapters.filter(c => c.status === ChapterStatus.COMPLETED);
    
    for (const chapter of completedChapters) {
      // 检查是否已有报告
      const existingReport = currentState.chapterReports.find(r => r.chapterId === chapter.id);
      if (!existingReport) {
        try {
          await generateChapterReport(chapter.id);
        } catch (error) {
          console.error(`❌ [批量报告] 生成章节${chapter.title}报告失败:`, error);
        }
      }
    }
    
    console.log('✅ [批量报告] 所有章节报告生成完成');
  }, [taskStore, generateChapterReport]);



  // 生成最终报告
  const generateFinalReport = useCallback(async () => {
    console.log('📊 [最终报告] 开始汇总所有章节报告');

    try {
      const currentState = taskStore.chapterResearch;
      const allReports = currentState.chapterReports
        .sort((a, b) => {
          // 按章节顺序排序
          const aIndex = currentState.chapters.findIndex(c => c.id === a.chapterId);
          const bIndex = currentState.chapters.findIndex(c => c.id === b.chapterId);
          return aIndex - bIndex;
        });

      // 1. 处理章节报告，提取参考来源部分
      const processedReports = allReports.map(report => {
        // 查找 "## 参考来源" 或 "### 参考来源" 的位置
        const refPattern = /\n##\s*参考来源[\s\S]*$/;
        const refMatch = report.content.match(refPattern);
        
        let cleanContent = report.content;
        let extractedRefs = '';
        
        if (refMatch) {
          // 提取参考来源部分（保留内容，但去掉标题行）
          const refsContent = refMatch[0];
          // 去掉 "## 参考来源" 标题行，只保留引用内容
          extractedRefs = refsContent.replace(/^\n##\s*参考来源\s*\n/, '');
          // 移除参考来源部分，保留正文内容
          cleanContent = report.content.substring(0, refMatch.index);
          console.log(`📋 [最终报告] 从章节"${report.title}"提取了参考来源`);
        }
        
        return {
          title: report.title,
          content: cleanContent,
          references: extractedRefs
        };
      });
      
      // 硬拼接处理后的章节内容（不包含参考来源）
      // 注意：章节报告内容本身已包含标题，不需要额外添加
      const hardConcatenatedContent = processedReports
        .map(report => report.content)
        .join('\n\n---\n\n');

      console.log('📝 [最终报告] 章节内容硬拼接完成（已移除各章节参考来源）');

      // 2. 拼接所有章节提取的参考来源（不去重）
      const allChapterReferences = processedReports
        .map(report => report.references)
        .filter(refs => refs && refs.trim() !== '')
        .join('\n\n');
      
      console.log(`📑 [最终报告] 拼接了${processedReports.filter(r => r.references).length}个章节的参考来源`);

      // 3. 生成总结性章节的prompt
      const summaryPrompt = `${chapterSummaryPrompt}

## 研究主题
${currentState.userQuery}

## 章节内容
以下是各章节的研究报告内容：

${processedReports.map(report => 
  `### ${report.title}
${report.content}`
).join('\n\n---\n\n')}
`;

      console.log('🤖 [最终报告] 开始生成总结性章节');

      // 4. 使用LLM生成总结性章节
      const model = await createModelProvider(getModel().thinkingModel);
      const result = await generateText({
        model,
        prompt: summaryPrompt,
        temperature: 0.5,
        maxTokens: 8192, // 降低token消耗，只生成总结内容
      });
      const summaryContent = result.text;

      console.log('✅ [最终报告] 总结性章节生成完成');

      // 5. 组装最终报告：章节内容 + 总结章节 + 引用来源
      const referencesSection = allChapterReferences.trim() !== ''
        ? `\n\n## 参考文献\n\n${allChapterReferences}`
        : '';

      const finalReport = [
        hardConcatenatedContent,
        '\n\n---\n\n',
        '## 研究总结\n\n',
        summaryContent,
        referencesSection
      ].join('');

      console.log('📋 [最终报告] 最终报告组装完成');

      // 更新最终报告
      taskStore.updateFinalReport(finalReport);
      taskStore.setChapterResearchRunning(false);

      console.log('✅ [最终报告] 章节式研究完成');

    } catch (error) {
      console.error('❌ [最终报告] 生成失败:', error);
      taskStore.setChapterResearchRunning(false);
      throw error;
    }
  }, [taskStore, createModelProvider, getModel]);

  // 简单的缓存机制
  const contextCache = useMemo(() => new Map<string, any>(), []);
  
  // 收集讨论上下文
  const collectDiscussionContext = useCallback((chapterId: string, roundNumber: number, currentTurnNumber?: number) => {
    const cacheKey = `${chapterId}-${roundNumber}-${currentTurnNumber || 'all'}`;
    
    // 检查缓存（仅缓存已完成轮次的上下文）
    if (currentTurnNumber === undefined && contextCache.has(cacheKey)) {
      console.log(`🔄 [缓存命中] 使用缓存的上下文: ${cacheKey}`);
      return contextCache.get(cacheKey);
    }
    
    const currentState = useTaskStore.getState().chapterResearch;
    const chapter = currentState.chapters.find(c => c.id === chapterId);
    
    console.log(`🔍 [上下文收集开始] chapterId: ${chapterId}, roundNumber: ${roundNumber}, currentTurnNumber: ${currentTurnNumber}`);
    console.log(`📚 [原始数据] 总讨论数: ${currentState.discussions.length}`);
    console.log(`📚 [原始数据] 所有讨论:`, currentState.discussions.map(d => `${d.chapterId}-R${d.roundNumber}-T${d.turnNumber}-${d.agentId}-${d.actionName}`));
    
    // 优化：一次过滤，按需分类 - 提高检索效率
    const chapterDiscussions = currentState.discussions.filter(d => d.chapterId === chapterId);
    console.log(`🎯 [章节过滤] 章节${chapterId}的讨论数: ${chapterDiscussions.length}`);
    console.log(`🎯 [章节过滤] 章节讨论详情:`, chapterDiscussions.map(d => `R${d.roundNumber}-T${d.turnNumber}-${d.agentId}-${d.actionName}`));
    
    const currentRoundDiscussions = chapterDiscussions.filter(d => 
      d.roundNumber === roundNumber &&
      (currentTurnNumber === undefined || d.turnNumber < currentTurnNumber)
    );
    console.log(`🔄 [当前轮次] R${roundNumber}的讨论数: ${currentRoundDiscussions.length} (turnNumber < ${currentTurnNumber || '无限制'})`);
    console.log(`🔄 [当前轮次] 当前轮次讨论详情:`, currentRoundDiscussions.map(d => `T${d.turnNumber}-${d.agentId}-${d.actionName}`));

    const previousDiscussions = chapterDiscussions.filter(d => 
      d.roundNumber < roundNumber
    );
    console.log(`⏪ [历史轮次] R<${roundNumber}的讨论数: ${previousDiscussions.length}`);

    // 从当前轮次中分别获取查询阶段的讨论
    const queryPhaseDiscussions = currentRoundDiscussions.filter(d => 
      d.actionName === 'QUERY_PROPOSE' || d.actionName === 'EXECUTE_QUERIES'
    );
    console.log(`🔍 [查询阶段] 查询相关讨论数: ${queryPhaseDiscussions.length}`);
    console.log(`🔍 [查询阶段] 查询讨论详情:`, queryPhaseDiscussions.map(d => `T${d.turnNumber}-${d.agentId}-${d.actionName}-${d.payload.queries?.length || 0}个查询`));

    // 获取配置信息
    const config = currentState.config;
    
    // 处理收集的信息部分
    const collectedInfoSection = config.includeCollectedInfo 
      ? getChapterCollectedSummary(chapterId, currentState.collectedInfo)
      : "(已禁用搜索信息输入)";
    
    // 优化：统一格式，提取关键信息，避免冗余JSON
    const formatDiscussionPayload = (d: AgentDiscussion): string => {
      if (d.actionName === 'HIGH_LEVEL_PROPOSE' && d.payload.proposal) {
        return `提议: ${d.payload.proposal}`;
      } else if (d.actionName === 'HIGH_LEVEL_AGREE' && d.payload.agreement) {
        return `共识: ${d.payload.agreement}`;
      } else if (d.actionName === 'QUERY_PROPOSE' && d.payload.queries) {
        const queries = d.payload.queries.map((q: any) => `"${q.query}" (目标: ${q.researchGoal})`).join(', ');
        return `查询提议: ${queries}`;
      } else if (d.actionName === 'EXECUTE_QUERIES' && d.payload.queries) {
        const queries = d.payload.queries.map((q: any) => `"${q.query}"`).join(', ');
        return `执行查询: ${queries}`;
      }
      return d.payload.rationale || '无具体内容';
    };

    const currentRoundDiscussionsText = currentRoundDiscussions
      .map(d => `回合${d.turnNumber} ${d.agentId}: ${d.thought}\n${formatDiscussionPayload(d)}`)
      .join('\n\n---\n\n');

    const previousDiscussionsText = previousDiscussions
      .map(d => `R${d.roundNumber}-T${d.turnNumber} ${d.agentId}: ${d.thought}`)
      .join('\n');

    // 专门为查询阶段生成上下文 - 使用统一格式
    const currentQueryDiscussionsText = queryPhaseDiscussions
      .map(d => `回合${d.turnNumber} ${d.agentId}: ${d.thought}\n${formatDiscussionPayload(d)}`)
      .join('\n\n---\n\n');

    const highLevelConsensus = chapter?.highLevelConsensus || '';
    
    // 收集历史数据
    const historicalConsensus = chapter?.historicalConsensus || [];
    const historicalQueries = chapter?.historicalQueries || [];
    
    // 获取第一章节预览（如果当前不是第一章节）
    const chapterIndex = currentState.chapters.findIndex(c => c.id === chapterId);
    const firstChapterPreview = chapterIndex > 0 ? currentState.firstChapterPreview : undefined;

    console.log(`📊 [上下文收集] 章节${chapterId}轮次${roundNumber}-${currentTurnNumber || '?'}: 当前轮次讨论${currentRoundDiscussions.length}个, 查询讨论${queryPhaseDiscussions.length}个, 历史讨论${previousDiscussions.length}个, 收集信息长度${collectedInfoSection.length}`);
    console.log(`🔍 [详细讨论]`, currentRoundDiscussions.map(d => `${d.agentId}-${d.actionName}`));
    console.log(`🔍 [查询讨论]`, queryPhaseDiscussions.map(d => `${d.agentId}-${d.actionName}`));
    console.log(`📚 [全部讨论]`, currentState.discussions.filter(d => d.chapterId === chapterId).map(d => `R${d.roundNumber}-T${d.turnNumber}-${d.agentId}-${d.actionName}`));
    console.log(`📋 [历史数据] 历史共识: ${historicalConsensus.length}个, 历史查询: ${historicalQueries.length}个`);
    console.log(`📑 [第一章节] 是否有第一章节预览: ${firstChapterPreview ? `是（${firstChapterPreview.length}字符）` : '否'}`);

    const result = {
      collectedInfoSection,  // 直接返回处理好的内容
      currentRoundDiscussions: currentRoundDiscussionsText,
      previousDiscussions: previousDiscussionsText,
      highLevelConsensus,
      currentQueryDiscussions: currentQueryDiscussionsText,
      // 历史数据
      historicalConsensus,
      historicalQueries,
      // 第一章节预览
      firstChapterPreview,
    };
    
    // 缓存已完成轮次的上下文（不缓存进行中的轮次）
    if (currentTurnNumber === undefined) {
      contextCache.set(cacheKey, result);
    }
    
    return result;
  }, [contextCache]);

  // 暂停研究
  const pauseResearch = useCallback(() => {
    taskStore.setChapterResearchRunning(false);
    console.log('⏸️ [章节研究] 已暂停');
  }, [taskStore]);

  // 重置研究
  const resetResearch = useCallback(() => {
    taskStore.resetChapterResearch();
    console.log('🔄 [章节研究] 已重置');
  }, [taskStore]);

  // 计算统计信息
  const stats = useMemo(() => {
    const chapters = Array.isArray(chapterResearch.chapters) ? chapterResearch.chapters : [];
    const collectedInfo = Array.isArray(chapterResearch.collectedInfo) ? chapterResearch.collectedInfo : [];
    
    // 收集章节数 - 已完成的章节数
    const totalChapters = chapters.length;
    const completedChapters = chapters.filter(c => c.status === ChapterStatus.COMPLETED).length;
    
    // 收集总轮数 - 计算已完成的轮数（不包括进行中的轮次）
    const totalRounds = chapters.reduce((sum, chapter) => {
      if (chapter.status === ChapterStatus.COMPLETED) {
        // 已完成章节，所有轮数都算已收集
        return sum + chapter.currentRound;
      } else if (chapter.currentRound > 0) {
        // 进行中章节，当前轮次不算，只算已完成的轮次
        return sum + (chapter.currentRound - 1);
      }
      return sum;
    }, 0);
    
    // 查询结果数 - 计算所有搜索结果的数量
    const totalQueries = collectedInfo.reduce((sum, info) => sum + (info.searchResults?.length || 0), 0);

    return {
      totalChapters,
      completedChapters, 
      totalRounds,
      totalQueries,
      progress: totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0,
    };
  }, [chapterResearch]);

  return {
    // 状态
    chapterResearch,
    isActive: chapterResearch.isActive,
    isRunning: chapterResearch.isRunning,
    currentChapter: chapterResearch.currentChapterId,
    stats,

    // 方法
    startChapterResearch,
    startChapterDiscussion,
    pauseResearch,
    resetResearch,

    // 报告生成方法
    generateChapterReport,
    generateAllChapterReports,
    generateFinalReport,

    // 配置方法
    showConfig: () => taskStore.setChapterResearchShowConfig(true),
    hideConfig: () => taskStore.setChapterResearchShowConfig(false),
    updateConfig: taskStore.updateChapterResearchConfig,
  };
}