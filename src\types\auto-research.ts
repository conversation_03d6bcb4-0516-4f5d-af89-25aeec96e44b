// 自动研究功能相关类型定义

/**
 * 自动研究配置
 */
export interface AutoResearchConfig {
  enabled: boolean;           // 是否启用自动研究
  maxRounds: number;         // 最大搜集轮数 (默认3)
  initialQueries: number;    // 初始查询数 (默认5)
  qualityThreshold: number;  // 质量阈值 (0-1, 默认0.7)
  maxDuration: number;       // 最长研究时间(分钟, 默认30)
}

/**
 * 自动研究运行时状态
 */
export interface AutoResearchRuntime {
  isRunning: boolean;        // 是否正在运行
  currentRound: number;      // 当前轮数
  startTime: number;         // 开始时间戳
  totalQueries: number;      // 总查询数
  completedQueries: number;  // 已完成查询数
}

/**
 * 研究轮次信息
 */
export interface ResearchRound {
  roundNumber: number;       // 轮次编号
  queries: string[];         // 该轮次的查询列表
  reflection?: ReflectionResult;  // 反思结果
  evaluation?: EvaluationResult;  // 评估结果
  startTime: number;         // 轮次开始时间
  endTime?: number;          // 轮次结束时间
}

/**
 * 反思分析结果 - 专注当前轮次内容质量分析
 */
export interface ReflectionResult {
  content_quality_score: number;      // 当前轮内容质量 (0-1)
  knowledge_completeness_score: number; // 当前轮知识完整性 (0-1)
  improvement_analysis: string;        // 相对上轮的改进程度描述 [前端显示]
  key_discoveries: string[];           // 当轮最重要的发现列表 [前端显示]
  knowledge_gaps: string[];            // 新发现的知识缺口列表
  follow_up_queries: Array<{           // 后续查询建议
    query: string;
    language: "chinese" | "english";
    researchGoal: string;
  }>;
}

/**
 * 质量评估结果 - 基于反思结果和全局信息的战略决策
 */
export interface EvaluationResult {
  should_continue: boolean;         // 是否继续研究
  strategic_recommendation: string; // 全局战略建议 [前端显示]
  coverage_completeness_score: number; // 整体覆盖完整性 (0-1)
  score_change_justification: string; // 分数变化理由 [前端显示]
}

/**
 * 自动研究状态
 */
export interface AutoResearchState {
  config: AutoResearchConfig;
  runtime: AutoResearchRuntime;
  rounds: ResearchRound[];
  showConfig: boolean;              // 是否显示配置面板
}

/**
 * 自动研究任务类型
 */
export type AutoResearchTaskType = 'search' | 'reflection' | 'evaluation' | 'chapter_start' | 'agent_discussion' | 'chapter_completed';

/**
 * 扩展的搜索任务接口（用于类型增强）
 */
export interface ExtendedSearchTask {
  roundNumber?: number;             // 所属轮次
  taskType?: AutoResearchTaskType;  // 任务类型
  parentRound?: number;             // 父轮次（用于反思和评估任务）
}

/**
 * 自动研究事件类型
 */
export type AutoResearchEvent = 
  | { type: 'config_updated'; config: AutoResearchConfig }
  | { type: 'research_started'; startTime: number }
  | { type: 'round_started'; roundNumber: number }
  | { type: 'round_completed'; roundNumber: number; round: ResearchRound }
  | { type: 'reflection_completed'; roundNumber: number; reflection: ReflectionResult }
  | { type: 'evaluation_completed'; roundNumber: number; evaluation: EvaluationResult }
  | { type: 'research_stopped'; reason: 'user_stopped' | 'time_exceeded' | 'quality_reached' | 'max_rounds_reached' }
  | { type: 'research_completed'; totalRounds: number; finalScore: number }
  | { type: 'error'; error: string; roundNumber?: number };

/**
 * 自动研究默认配置
 */
export const DEFAULT_AUTO_RESEARCH_CONFIG: AutoResearchConfig = {
  enabled: false,
  maxRounds: 3,
  initialQueries: 5,
  qualityThreshold: 0.7,
  maxDuration: 30, // 30分钟
};

/**
 * 自动研究默认运行时状态
 */
export const DEFAULT_AUTO_RESEARCH_RUNTIME: AutoResearchRuntime = {
  isRunning: false,
  currentRound: 0,
  startTime: 0,
  totalQueries: 0,
  completedQueries: 0,
};

/**
 * 自动研究默认状态
 */
export const DEFAULT_AUTO_RESEARCH_STATE: AutoResearchState = {
  config: DEFAULT_AUTO_RESEARCH_CONFIG,
  runtime: DEFAULT_AUTO_RESEARCH_RUNTIME,
  rounds: [],
  showConfig: false,
};

/**
 * 自动研究配置验证
 */
export function validateAutoResearchConfig(config: Partial<AutoResearchConfig>): AutoResearchConfig {
  const validated = {
    enabled: config.enabled ?? DEFAULT_AUTO_RESEARCH_CONFIG.enabled,
    maxRounds: Math.max(1, Math.min(10, config.maxRounds ?? DEFAULT_AUTO_RESEARCH_CONFIG.maxRounds)),
    initialQueries: Math.max(1, Math.min(10, config.initialQueries ?? DEFAULT_AUTO_RESEARCH_CONFIG.initialQueries)),
    qualityThreshold: Math.max(0.05, Math.min(1.0, config.qualityThreshold ?? DEFAULT_AUTO_RESEARCH_CONFIG.qualityThreshold)),
    maxDuration: Math.max(2, Math.min(30, config.maxDuration ?? DEFAULT_AUTO_RESEARCH_CONFIG.maxDuration)), // 2-30分钟
  };

  // 额外的业务逻辑验证
  if (validated.maxRounds > 3 && validated.maxDuration < 20) {
    console.warn("High round count with low duration may not provide sufficient time for quality research");
  }

  if (validated.qualityThreshold > 0.9 && validated.maxRounds < 3) {
    console.warn("High quality threshold with low round count may result in insufficient research depth");
  }

  return validated;
}

/**
 * 检查是否超时
 */
export function isTimeExceeded(startTime: number, maxDuration: number): boolean {
  const elapsedMinutes = (Date.now() - startTime) / (1000 * 60);
  return elapsedMinutes >= maxDuration;
}

/**
 * 格式化轮次标识
 */
export function formatRoundLabel(roundNumber: number, taskType?: AutoResearchTaskType): string {
  const baseLabel = `[第${roundNumber}轮]`;
  
  switch (taskType) {
    case 'reflection':
      return `${baseLabel}-反思`;
    case 'evaluation':
      return `${baseLabel}-评估`;
    case 'chapter_start':
      return `[章节${roundNumber}]-开始`;
    case 'agent_discussion':
      return `[章节${roundNumber}]-讨论`;
    case 'chapter_completed':
      return `[章节${roundNumber}]-完成`;
    case 'search':
    default:
      return baseLabel;
  }
}

/**
 * 生成反思任务查询标识
 */
export function generateReflectionQuery(roundNumber: number): string {
  return `知识缺口分析-第${roundNumber}轮`;
}

/**
 * 生成评估任务查询标识
 */
export function generateEvaluationQuery(roundNumber: number): string {
  return `研究质量评估-第${roundNumber}轮`;
}
